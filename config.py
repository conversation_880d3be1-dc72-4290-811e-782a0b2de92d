import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

class Config:
    """基础配置类"""
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'dev-key-for-jiaoshi-project'
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or 'sqlite:///jiaoshi.db'
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    
    # AI模型配置
    AI_API_TYPE = os.environ.get('AI_API_TYPE') or 'dashscope'  # 'dashscope' 或 'openai'
    AI_API_KEY = os.environ.get('AI_API_KEY') or ''
    
    # 百炼API配置
    AI_APP_ID = os.environ.get('AI_APP_ID') or ''  # 百炼应用ID
    
    # OpenAI配置
    AI_MODEL = os.environ.get('AI_MODEL') or 'gpt-3.5-turbo'
    AI_ENDPOINT = os.environ.get('AI_ENDPOINT') or 'https://api.openai.com/v1/chat/completions'
    
    # 应用配置
    APP_NAME = "智慧成本核算教学系统"
    ADMIN_EMAIL = os.environ.get('ADMIN_EMAIL') or '<EMAIL>'
    
    # 分页配置
    ITEMS_PER_PAGE = 10

class DevelopmentConfig(Config):
    """开发环境配置"""
    DEBUG = True
    
class ProductionConfig(Config):
    """生产环境配置"""
    DEBUG = False
    # 生产环境应使用更安全的密钥
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'production-key-must-be-changed'
    
class TestingConfig(Config):
    """测试环境配置"""
    TESTING = True
    SQLALCHEMY_DATABASE_URI = 'sqlite:///:memory:'
    
# 配置字典
config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'testing': TestingConfig,
    'default': DevelopmentConfig
}
