{% extends "base.html" %}

{% block title %}登录 - 智慧成本核算教学系统{% endblock %}

{% block styles %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/login.css') }}">
{% endblock %}

{% block content %}
<div class="login-container animate__animated animate__fadeIn">
    <div class="row">
        <!-- 左侧图片区域 -->
        <div class="col-md-6 login-image-container">
            <div class="login-image">
                <img src="{{ url_for('static', filename='images/login-bg.jpg') }}" alt="智慧成本核算教学系统" class="img-fluid">
                <div class="login-overlay">
                    <div class="animate__animated animate__fadeInUp">
                        <h2><i class="fas fa-graduation-cap me-2"></i>智慧成本核算教学系统</h2>
                        <p>提升教学质量，促进学生理解</p>
                        <div class="mt-4 features">
                            <div class="feature-item"><i class="fas fa-robot me-2"></i>智能对话</div>
                            <div class="feature-item"><i class="fas fa-chart-line me-2"></i>数据分析</div>
                            <div class="feature-item"><i class="fas fa-lightbulb me-2"></i>提示词库</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 右侧登录表单 -->
        <div class="col-md-6 login-form-container">
            <div class="login-form animate__animated animate__fadeInRight">
                <div class="text-center mb-4">
                    <div class="app-logo mb-3">
                        <i class="fas fa-graduation-cap"></i>
                    </div>
                    <h3>用户登录</h3>
                    <p class="text-muted">欢迎使用智慧成本核算教学系统</p>
                </div>
                
                <form method="POST" action="{{ url_for('auth.login') }}">
                    <div class="mb-4">
                        <label for="username" class="form-label">用户名</label>
                        <div class="input-group input-group-lg">
                            <span class="input-group-text"><i class="fas fa-user"></i></span>
                            <input type="text" class="form-control" id="username" name="username" required placeholder="请输入用户名">
                        </div>
                    </div>
                    
                    <div class="mb-4">
                        <label for="password" class="form-label">密码</label>
                        <div class="input-group input-group-lg">
                            <span class="input-group-text"><i class="fas fa-lock"></i></span>
                            <input type="password" class="form-control" id="password" name="password" required placeholder="请输入密码">
                        </div>
                    </div>
                    
                    <div class="mb-4 form-check">
                        <input type="checkbox" class="form-check-input" id="remember" name="remember">
                        <label class="form-check-label" for="remember">记住我</label>
                    </div>
                    
                    <div class="d-grid mb-4">
                        <button type="submit" class="btn btn-primary btn-lg">
                            <i class="fas fa-sign-in-alt me-2"></i>登录
                        </button>
                    </div>
                    
                    <div class="text-center text-muted">
                        <small>默认管理员账号: admin / admin123</small>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
