{% extends "base.html" %}

{% block title %}系统设置 - 智慧成本核算教学系统{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="row justify-content-center">
        <div class="col-md-10">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-cogs me-2"></i>系统设置</h5>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ url_for('system.settings') }}">
                        <h5 class="mb-3 border-bottom pb-2">AI服务配置</h5>
                        
                        <div class="mb-3">
                            <label class="form-label">AI服务类型</label>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="ai_api_type" id="api_type_dashscope" value="dashscope" {% if config.AI_API_TYPE == 'dashscope' %}checked{% endif %}>
                                <label class="form-check-label" for="api_type_dashscope">
                                    百炼API (阿里云)
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="ai_api_type" id="api_type_openai" value="openai" {% if config.AI_API_TYPE == 'openai' %}checked{% endif %}>
                                <label class="form-check-label" for="api_type_openai">
                                    OpenAI API
                                </label>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="ai_api_key" class="form-label">API密钥</label>
                            <input type="password" class="form-control" id="ai_api_key" name="ai_api_key" value="{{ config.AI_API_KEY }}" autocomplete="off">
                            <div class="form-text">AI服务的API密钥，用于访问AI服务</div>
                        </div>
                        
                        <div id="dashscope_settings" class="mb-3 {% if config.AI_API_TYPE != 'dashscope' %}d-none{% endif %}">
                            <label for="ai_app_id" class="form-label">百炼应用ID</label>
                            <input type="text" class="form-control" id="ai_app_id" name="ai_app_id" value="{{ config.AI_APP_ID }}">
                            <div class="form-text">百炼应用的ID，在百炼控制台中创建应用后获取</div>
                        </div>
                        
                        <div id="openai_settings" class="mb-3 {% if config.AI_API_TYPE != 'openai' %}d-none{% endif %}">
                            <div class="mb-3">
                                <label for="ai_model" class="form-label">OpenAI模型</label>
                                <input type="text" class="form-control" id="ai_model" name="ai_model" value="{{ config.AI_MODEL }}">
                                <div class="form-text">OpenAI的模型名称，如gpt-3.5-turbo</div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="ai_endpoint" class="form-label">API端点</label>
                                <input type="text" class="form-control" id="ai_endpoint" name="ai_endpoint" value="{{ config.AI_ENDPOINT }}">
                                <div class="form-text">OpenAI API的端点URL</div>
                            </div>
                        </div>
                        
                        <div class="d-flex justify-content-end">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>保存设置
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const apiTypeRadios = document.querySelectorAll('input[name="ai_api_type"]');
        const dashscopeSettings = document.getElementById('dashscope_settings');
        const openaiSettings = document.getElementById('openai_settings');
        
        // 根据选择的API类型显示对应的设置
        apiTypeRadios.forEach(radio => {
            radio.addEventListener('change', function() {
                if (this.value === 'dashscope') {
                    dashscopeSettings.classList.remove('d-none');
                    openaiSettings.classList.add('d-none');
                } else if (this.value === 'openai') {
                    dashscopeSettings.classList.add('d-none');
                    openaiSettings.classList.remove('d-none');
                }
            });
        });
    });
</script>
{% endblock %} 