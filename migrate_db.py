import sqlite3
import os
from pathlib import Path

def migrate_database():
    """更新数据库结构，添加缺少的列"""
    # 获取数据库文件路径
    db_path = Path('instance/jiaoshi.db')
    
    if not db_path.exists():
        print(f"数据库文件不存在: {db_path}")
        return False
    
    try:
        # 连接数据库
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查 users 表中是否存在 user_type 列
        cursor.execute("PRAGMA table_info(users)")
        columns = [column[1] for column in cursor.fetchall()]
        
        # 如果不存在 user_type 列，则添加
        if 'user_type' not in columns:
            print("添加 user_type 列到 users 表...")
            cursor.execute("ALTER TABLE users ADD COLUMN user_type VARCHAR(20) DEFAULT 'student'")
            
            # 将管理员用户的 user_type 设置为 teacher
            cursor.execute("UPDATE users SET user_type = 'teacher' WHERE username = 'admin'")
            
            conn.commit()
            print("users表迁移成功！")
        else:
            print("user_type 列已存在，无需迁移")
        
        # 检查 chat_sessions 表中是否存在 prompt_id 列
        cursor.execute("PRAGMA table_info(chat_sessions)")
        chat_session_columns = [column[1] for column in cursor.fetchall()]
        
        # 如果不存在 prompt_id 列，则添加
        if 'prompt_id' not in chat_session_columns:
            print("添加 prompt_id 列到 chat_sessions 表...")
            cursor.execute("ALTER TABLE chat_sessions ADD COLUMN prompt_id INTEGER REFERENCES prompts(id)")
            conn.commit()
            print("chat_sessions表迁移成功！")
        else:
            print("prompt_id 列已存在，无需迁移")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"数据库迁移失败: {e}")
        return False

if __name__ == "__main__":
    migrate_database() 