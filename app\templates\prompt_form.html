{% extends "base.html" %}

{% block title %}
    {% if is_edit %}编辑提示词{% else %}创建提示词{% endif %} - 智慧成本核算教学系统
{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">{% if is_edit %}编辑提示词{% else %}创建提示词{% endif %}</h5>
                </div>
                <div class="card-body">
                    <form method="POST" action="{% if is_edit %}{{ url_for('prompt.edit', prompt_id=prompt.id) }}{% else %}{{ url_for('prompt.create') }}{% endif %}">
                        <div class="mb-3">
                            <label for="title" class="form-label">标题</label>
                            <input type="text" class="form-control" id="title" name="title" value="{% if prompt %}{{ prompt.title }}{% endif %}" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="content" class="form-label">内容</label>
                            <textarea class="form-control" id="content" name="content" rows="8" required>{% if prompt %}{{ prompt.content }}{% endif %}</textarea>
                            <div class="form-text">使用 {参数名} 标记可替换的参数</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="category_id" class="form-label">分类</label>
                            <select class="form-select" id="category_id" name="category_id" required>
                                <option value="">选择分类</option>
                                {% for category in categories %}
                                <option value="{{ category.id }}" {% if prompt and prompt.category_id == category.id %}selected{% endif %}>
                                    {{ category.name }}
                                </option>
                                {% endfor %}
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label for="tags" class="form-label">标签</label>
                            <input type="text" class="form-control" id="tags" name="tags" value="{% if prompt %}{{ prompt.tags }}{% endif %}">
                            <div class="form-text">多个标签用逗号分隔</div>
                        </div>
                        
                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="is_public" name="is_public" {% if not prompt or prompt.is_public %}checked{% endif %}>
                            <label class="form-check-label" for="is_public">公开给所有用户</label>
                        </div>
                        
                        <div class="d-flex justify-content-between">
                            <a href="{{ url_for('prompt.index') }}" class="btn btn-secondary">取消</a>
                            <button type="submit" class="btn btn-primary">{% if is_edit %}保存修改{% else %}创建提示词{% endif %}</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 