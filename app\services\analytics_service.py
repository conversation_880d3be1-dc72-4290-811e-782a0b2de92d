from collections import Counter
import re
from app.models import db
from app.models.chat import ChatSession, ChatMessage
from app.models.user import User, Role
from app.models.prompt import PromptCategory, Prompt
from sqlalchemy import func, desc, case
from datetime import datetime, timed<PERSON>ta

def generate_keyword_cloud(keywords_data):
    """生成词云数据"""
    # 提取所有关键词
    all_keywords = []
    for row in keywords_data:
        if row[0]:  # 确保关键词不为空
            keywords = row[0].split(',')
            all_keywords.extend(keywords)
    
    # 统计词频
    word_count = Counter(all_keywords)
    
    # 格式化为词云数据
    cloud_data = [{"text": word, "weight": count} for word, count in word_count.most_common(50)]
    
    return cloud_data

def get_question_type_stats(results):
    """获取问题类型统计数据"""
    categories = []
    counts = []
    
    for category, count in results:
        categories.append(category)
        counts.append(count)
    
    chart_data = {
        'labels': categories,
        'datasets': [{
            'data': counts,
            'backgroundColor': [
                '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0', '#9966FF', '#FF9F40'
            ]
        }]
    }
    
    return chart_data

def get_interaction_stats(teacher_messages, student_messages, days=30):
    """获取师生互动统计数据"""
    # 按日期分组
    teacher_by_date = group_by_date(teacher_messages, days)
    student_by_date = group_by_date(student_messages, days)
    
    # 合并日期
    all_dates = sorted(set(list(teacher_by_date.keys()) + list(student_by_date.keys())))
    
    # 准备图表数据
    chart_data = {
        'labels': all_dates,
        'datasets': [
            {
                'label': '教师消息',
                'data': [teacher_by_date.get(date, 0) for date in all_dates],
                'borderColor': '#36A2EB',
                'backgroundColor': 'rgba(54, 162, 235, 0.2)'
            },
            {
                'label': '学生消息',
                'data': [student_by_date.get(date, 0) for date in all_dates],
                'borderColor': '#FF6384',
                'backgroundColor': 'rgba(255, 99, 132, 0.2)'
            }
        ]
    }
    
    return chart_data

def group_by_date(messages, days=30):
    """按日期分组消息"""
    result = {}
    
    for msg in messages:
        date_str = msg.created_at.strftime('%Y-%m-%d')
        result[date_str] = result.get(date_str, 0) + 1
    
    return result

def analyze_prompt_effectiveness(prompts, messages):
    """分析提示词有效性"""
    effectiveness = {}
    
    for prompt in prompts:
        # 获取使用此提示词的消息
        prompt_messages = [msg for msg in messages if msg.session.prompt_id == prompt.id]
        
        if prompt_messages:
            # 计算平均token长度
            avg_tokens = sum(msg.tokens for msg in prompt_messages) / len(prompt_messages)
            
            # 计算问题类型分布
            categories = Counter(msg.category for msg in prompt_messages if msg.category)
            
            effectiveness[prompt.id] = {
                'title': prompt.title,
                'usage_count': prompt.usage_count,
                'avg_tokens': avg_tokens,
                'categories': dict(categories)
            }
    
    return effectiveness

def get_chat_statistics():
    """获取聊天统计数据
    
    Returns:
        dict: 包含各种统计数据的字典
    """
    # 基础统计
    total_users = User.query.join(User.roles).filter(Role.name == 'student').count()
    total_sessions = ChatSession.query.count()
    total_messages = ChatMessage.query.count()
    
    # 活跃用户统计
    active_users = db.session.query(
        User.username, 
        User.name, 
        func.count(ChatMessage.id).label('message_count')
    ).join(
        ChatSession, ChatSession.user_id == User.id
    ).join(
        ChatMessage, ChatMessage.session_id == ChatSession.id
    ).group_by(
        User.id
    ).order_by(
        desc('message_count')
    ).limit(5).all()
    
    active_users_data = [
        {
            'username': username,
            'name': name,
            'message_count': message_count
        }
        for username, name, message_count in active_users
    ]
    
    # 每日消息统计
    today = datetime.now().date()
    past_week = today - timedelta(days=7)
    
    daily_stats = db.session.query(
        func.date(ChatMessage.created_at).label('date'),
        func.count(ChatMessage.id).label('count')
    ).filter(
        func.date(ChatMessage.created_at) >= past_week
    ).group_by(
        'date'
    ).order_by(
        'date'
    ).all()
    
    # 确保即使没有数据也返回有效格式
    if not daily_stats:
        # 如果没有数据，创建过去7天的空数据
        daily_data = {
            'labels': [(today - timedelta(days=i)).strftime('%Y-%m-%d') for i in range(7, 0, -1)],
            'data': [0] * 7
        }
    else:
        daily_data = {
            'labels': [str(date) for date, _ in daily_stats],
            'data': [count for _, count in daily_stats]
        }
    
    # 问题分类统计
    category_stats = db.session.query(
        PromptCategory.name,
        func.count(ChatMessage.id).label('count')
    ).join(
        ChatMessage, ChatMessage.category_id == PromptCategory.id
    ).filter(
        ChatMessage.role == 'user'
    ).group_by(
        PromptCategory.id
    ).order_by(
        desc('count')
    ).all()
    
    # 确保即使没有数据也返回有效格式
    if not category_stats:
        category_data = {
            'labels': ['暂无数据'],
            'data': [0],
            'backgroundColor': ['#e9ecef']
        }
    else:
        category_data = {
            'labels': [name for name, _ in category_stats],
            'data': [count for _, count in category_stats],
            'backgroundColor': [
                '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0', '#9966FF', '#FF9F40',
                '#FF8C00', '#20B2AA', '#8A2BE2', '#32CD32', '#DC143C', '#00BFFF'
            ][:len(category_stats)]
        }
    
    # 问题解决率统计
    resolution_stats = db.session.query(
        PromptCategory.name,
        func.sum(case((ChatMessage.is_resolved == True, 1), else_=0)).label('resolved'),
        func.sum(case((ChatMessage.is_resolved == False, 1), else_=0)).label('unresolved'),
        func.count(ChatMessage.id).label('total')
    ).join(
        ChatMessage, ChatMessage.category_id == PromptCategory.id
    ).filter(
        ChatMessage.role == 'user',
        ChatMessage.is_resolved != None
    ).group_by(
        PromptCategory.id
    ).all()
    
    resolution_data = []
    total_resolved = 0
    total_questions = 0
    
    if resolution_stats:
        for name, resolved, unresolved, total in resolution_stats:
            if total > 0:
                resolution_rate = round((resolved / total) * 100, 1)
            else:
                resolution_rate = 0
            
            resolution_data.append({
                'category': name,
                'resolved': resolved,
                'unresolved': unresolved,
                'total': total,
                'resolution_rate': resolution_rate
            })
        
        # 总体解决率
        total_resolved = sum(item['resolved'] for item in resolution_data)
        total_questions = sum(item['total'] for item in resolution_data)
    
    # 如果没有解决率数据，添加默认数据
    if not resolution_data:
        resolution_data = [{
            'category': '暂无数据',
            'resolved': 0,
            'unresolved': 0,
            'total': 0,
            'resolution_rate': 0
        }]
    
    overall_resolution_rate = round((total_resolved / total_questions) * 100, 1) if total_questions > 0 else 0
    
    # 组合所有统计数据
    return {
        'summary': {
            'total_users': total_users,
            'total_sessions': total_sessions,
            'total_messages': total_messages,
            'overall_resolution_rate': overall_resolution_rate
        },
        'active_users': active_users_data,
        'daily_messages': daily_data,
        'category_stats': category_data,
        'resolution_stats': resolution_data,
        'prompt_stats': get_prompt_statistics(),
        'top_questions': get_top_student_questions()
    }

def get_prompt_statistics():
    """获取提示词使用统计
    
    Returns:
        dict: 提示词统计数据
    """
    # 获取提示词使用次数
    prompt_usage = db.session.query(
        Prompt.id,
        Prompt.title,
        Prompt.category_id,
        PromptCategory.name.label('category_name'),
        func.count(ChatSession.id).label('usage_count')
    ).outerjoin(
        ChatSession, ChatSession.prompt_id == Prompt.id
    ).join(
        PromptCategory, PromptCategory.id == Prompt.category_id
    ).group_by(
        Prompt.id
    ).order_by(
        desc('usage_count')
    ).limit(10).all()
    
    # 格式化数据
    prompt_data = []
    categories = []
    usage_counts = []
    
    for prompt in prompt_usage:
        prompt_data.append({
            'id': prompt.id,
            'title': prompt.title,
            'category': prompt.category_name,
            'usage_count': prompt.usage_count
        })
        categories.append(prompt.category_name)
        usage_counts.append(prompt.usage_count)
    
    # 确保即使没有数据也返回有效格式
    if not prompt_data:
        return {
            'prompts': [],
            'chart_data': {
                'labels': ['暂无数据'],
                'data': [0]
            }
        }
    
    return {
        'prompts': prompt_data,
        'chart_data': {
            'labels': [p['title'] for p in prompt_data],
            'data': [p['usage_count'] for p in prompt_data]
        }
    }

def get_top_student_questions():
    """获取学生最常问的问题列表
    
    Returns:
        list: 学生问题列表
    """
    # 获取学生问题及其频率
    # 这里我们使用消息内容的前50个字符作为问题标识
    questions = db.session.query(
        func.substring(ChatMessage.content, 1, 50).label('question'),
        func.count(ChatMessage.id).label('frequency'),
        PromptCategory.name.label('category')
    ).outerjoin(
        PromptCategory, PromptCategory.id == ChatMessage.category_id
    ).filter(
        ChatMessage.role == 'user'
    ).group_by(
        'question', 'category'
    ).order_by(
        desc('frequency')
    ).limit(20).all()
    
    # 格式化数据
    question_data = []
    
    for q in questions:
        question_text = q.question
        if len(question_text) >= 50:
            question_text = question_text + '...'
            
        question_data.append({
            'question': question_text,
            'frequency': q.frequency,
            'category': q.category or '未分类'
        })
    
    return question_data
