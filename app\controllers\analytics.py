from flask import render_template, jsonify, send_file, request
import io
import xlsxwriter
from flask_login import login_required, current_user
from . import analytics_bp
from app.models.chat import ChatSession, ChatMessage
from app.services.analytics_service import get_chat_statistics

@analytics_bp.route('/')
@login_required
def index():
    """分析页面"""
    if not current_user.is_teacher():
        return render_template('error.html', error_code=403, error_message='只有教师可以访问此页面'), 403
    
    return render_template('analytics.html')

@analytics_bp.route('/data')
@login_required
def get_data():
    """获取分析数据"""
    if not current_user.is_teacher():
        return jsonify({'success': False, 'message': '无权访问此数据'}), 403
    
    # 获取聊天统计数据
    stats = get_chat_statistics()
    
    return jsonify({
        'success': True,
        'data': stats
    })

@analytics_bp.route('/export_excel')
@login_required
def export_excel():
    """导出分析数据为Excel"""
    if not current_user.is_teacher():
        return jsonify({'success': False, 'message': '无权访问此数据'}), 403
    
    stats = get_chat_statistics()
    output = io.BytesIO()
    workbook = xlsxwriter.Workbook(output, {'in_memory': True})
    
    # 概览
    summary_ws = workbook.add_worksheet('概览')
    summary_ws.write_row(0, 0, ['学生总数', '会话总数', '消息总数', '问题解决率'])
    summary = stats['summary']
    summary_ws.write_row(1, 0, [summary['total_users'], summary['total_sessions'], summary['total_messages'], f"{summary['overall_resolution_rate']}%"])
    
    # 活跃用户
    users_ws = workbook.add_worksheet('活跃用户')
    users_ws.write_row(0, 0, ['用户名', '姓名', '消息数'])
    for i, user in enumerate(stats['active_users'], 1):
        users_ws.write_row(i, 0, [user['username'], user['name'], user['message_count']])
    
    # 每日消息
    daily_ws = workbook.add_worksheet('每日消息')
    daily_ws.write_row(0, 0, ['日期', '消息数'])
    for i, (date, count) in enumerate(zip(stats['daily_messages']['labels'], stats['daily_messages']['data']), 1):
        daily_ws.write_row(i, 0, [date, count])
    
    # 问题分类
    cat_ws = workbook.add_worksheet('问题分类')
    cat_ws.write_row(0, 0, ['分类', '数量'])
    for i, (label, count) in enumerate(zip(stats['category_stats']['labels'], stats['category_stats']['data']), 1):
        cat_ws.write_row(i, 0, [label, count])
    
    # 解决率
    res_ws = workbook.add_worksheet('解决率')
    res_ws.write_row(0, 0, ['分类', '已解决', '未解决', '总数', '解决率'])
    for i, item in enumerate(stats['resolution_stats'], 1):
        res_ws.write_row(i, 0, [item['category'], item['resolved'], item['unresolved'], item['total'], f"{item['resolution_rate']}%"])
    
    workbook.close()
    output.seek(0)
    return send_file(output, as_attachment=True, download_name='数据分析统计.xlsx', mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
