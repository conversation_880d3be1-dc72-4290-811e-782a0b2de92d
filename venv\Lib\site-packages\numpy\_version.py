
# This file was generated by 'versioneer.py' (0.26) from
# revision-control system data, or from the parent directory name of an
# unpacked source archive. Distribution tarballs contain a pre-generated copy
# of this file.

import json

version_json = '''
{
 "date": "2023-02-05T11:25:52-0500",
 "dirty": false,
 "error": null,
 "full-revisionid": "85f38ab180ece5290f64e8ddbd9cf06ad8fa4a5e",
 "version": "1.24.2"
}
'''  # END VERSION_JSON


def get_versions():
    return json.loads(version_json)
