from app.models import db
from app.models.user import User, Role
from app.models.prompt import PromptCategory, Prompt
from werkzeug.security import generate_password_hash

def init_db(app):
    """初始化数据库，创建默认数据"""
    with app.app_context():
        # 创建表
        db.create_all()
        
        # 创建角色
        create_roles()
        
        # 创建管理员用户
        create_admin_user()
        
        # 创建默认提示词分类
        create_default_categories()
        
        # 创建示例提示词
        create_sample_prompts()
        
        db.session.commit()

def create_roles():
    """创建角色"""
    # 检查角色是否已存在
    if Role.query.filter_by(name='teacher').first() is None:
        teacher_role = Role(name='teacher', description='教师角色，拥有管理权限')
        db.session.add(teacher_role)
    
    if Role.query.filter_by(name='student').first() is None:
        student_role = Role(name='student', description='学生角色，仅可访问基础功能')
        db.session.add(student_role)
    
    db.session.commit()

def create_admin_user():
    """创建管理员用户"""
    # 检查管理员是否已存在
    if User.query.filter_by(username='admin').first() is None:
        admin = User(
            username='admin',
            email='<EMAIL>',
            name='管理员',
            password_hash=generate_password_hash('admin123'),
            is_active=True
        )
        
        # 分配教师角色
        teacher_role = Role.query.filter_by(name='teacher').first()
        if teacher_role:
            admin.roles.append(teacher_role)
        
        db.session.add(admin)
        db.session.commit()

def create_default_categories():
    """创建默认提示词分类"""
    default_categories = [
        ('成本核算基础', '基础概念与计算方法'),
        ('成本分析', '成本数据分析与解读'),
        ('成本控制', '成本控制策略与方法'),
        ('案例分析', '实际案例与问题解决')
    ]
    
    for name, desc in default_categories:
        if PromptCategory.query.filter_by(name=name).first() is None:
            category = PromptCategory(name=name, description=desc)
            db.session.add(category)
    
    db.session.commit()

def create_sample_prompts():
    """创建示例提示词"""
    # 获取管理员用户和分类
    admin = User.query.filter_by(username='admin').first()
    basic_category = PromptCategory.query.filter_by(name='成本核算基础').first()
    analysis_category = PromptCategory.query.filter_by(name='成本分析').first()
    
    if not admin or not basic_category or not analysis_category:
        return
    
    sample_prompts = [
        {
            'title': '成本要素分析',
            'content': '请分析以下生产过程中的成本要素构成，并计算总成本：{原材料成本}、{人工成本}、{制造费用}。',
            'category': basic_category,
            'tags': '成本要素,计算,基础',
            'is_public': True
        },
        {
            'title': '边际成本计算',
            'content': '请计算当产量从{初始产量}增加到{目标产量}时的边际成本。已知总成本函数为：{成本函数}。',
            'category': basic_category,
            'tags': '边际成本,计算,函数',
            'is_public': True
        },
        {
            'title': '成本差异分析',
            'content': '请分析以下实际成本与标准成本之间的差异原因：标准成本{标准成本}，实际成本{实际成本}。',
            'category': analysis_category,
            'tags': '差异分析,标准成本,实际成本',
            'is_public': True
        }
    ]
    
    for prompt_data in sample_prompts:
        # 检查提示词是否已存在
        if Prompt.query.filter_by(title=prompt_data['title']).first() is None:
            prompt = Prompt(
                title=prompt_data['title'],
                content=prompt_data['content'],
                category=prompt_data['category'],
                tags=prompt_data['tags'],
                is_public=prompt_data['is_public'],
                created_by=admin.id
            )
            db.session.add(prompt)
    
    db.session.commit()
