from flask_login import UserMixin
from werkzeug.security import generate_password_hash, check_password_hash
from datetime import datetime
from . import db, login_manager

# 用户角色关联表
UserRole = db.Table('user_roles',
    db.Column('user_id', db.Integer, db.<PERSON>('users.id'), primary_key=True),
    db.Column('role_id', db.Integer, db.<PERSON>ey('roles.id'), primary_key=True)
)

class Role(db.Model):
    """角色模型"""
    __tablename__ = 'roles'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(64), unique=True, index=True)
    description = db.Column(db.String(255))
    
    def __repr__(self):
        return f'<Role {self.name}>'

class User(UserMixin, db.Model):
    """用户模型"""
    __tablename__ = 'users'
    
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(64), unique=True, index=True)
    email = db.Column(db.String(120), unique=True, index=True)
    name = db.Column(db.String(64))
    password_hash = db.Column(db.String(128))
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    last_login = db.Column(db.DateTime)
    user_type = db.Column(db.String(20), default='student')  # 用户类型：teacher或student
    
    # 关系
    roles = db.relationship('Role', secondary=UserRole, backref=db.backref('users', lazy='dynamic'))
    chat_sessions = db.relationship('ChatSession', backref='user', lazy='dynamic')
    prompts = db.relationship('Prompt', backref='creator', lazy='dynamic')
    
    @property
    def password(self):
        raise AttributeError('密码不是可读属性')
    
    @password.setter
    def password(self, password):
        self.password_hash = generate_password_hash(password)
    
    def verify_password(self, password):
        return check_password_hash(self.password_hash, password)
    
    def has_role(self, role_name):
        """检查用户是否拥有指定角色"""
        return any(role.name == role_name for role in self.roles)
    
    def is_admin(self):
        """检查用户是否为管理员"""
        return self.username == 'admin'
    
    def is_teacher(self):
        """检查用户是否为教师"""
        return self.user_type == 'teacher' or self.is_admin()
    
    def is_student(self):
        """检查用户是否为学生"""
        return self.user_type == 'student' and not self.is_admin()
    
    def __repr__(self):
        return f'<User {self.username}>'

@login_manager.user_loader
def load_user(user_id):
    """加载用户的回调函数"""
    return User.query.get(int(user_id))
