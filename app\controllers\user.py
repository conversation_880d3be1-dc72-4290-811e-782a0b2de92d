from flask import render_template, redirect, url_for, flash, request, abort
from flask_login import login_required, current_user
from werkzeug.security import check_password_hash, generate_password_hash
from . import user_bp
from app.models import db
from app.models.user import User, Role

@user_bp.route('/profile')
@login_required
def profile():
    """用户个人资料页面"""
    return render_template('profile.html', user=current_user)

@user_bp.route('/profile/update', methods=['POST'])
@login_required
def update_profile():
    """更新用户资料"""
    name = request.form.get('name')
    email = request.form.get('email')
    
    if name:
        current_user.name = name
    
    if email:
        # 检查邮箱是否已被使用
        existing_user = User.query.filter(User.email == email, User.id != current_user.id).first()
        if existing_user:
            flash('该邮箱已被使用', 'danger')
        else:
            current_user.email = email
    
    db.session.commit()
    flash('个人资料已更新', 'success')
    return redirect(url_for('user.profile'))

@user_bp.route('/profile/update_password', methods=['POST'])
@login_required
def update_password():
    """更新用户密码"""
    current_password = request.form.get('current_password')
    new_password = request.form.get('new_password')
    confirm_password = request.form.get('confirm_password')
    
    # 验证当前密码
    if not check_password_hash(current_user.password_hash, current_password):
        flash('当前密码不正确', 'danger')
        return redirect(url_for('user.profile'))
    
    # 验证新密码
    if new_password != confirm_password:
        flash('两次输入的新密码不一致', 'danger')
        return redirect(url_for('user.profile'))
    
    if len(new_password) < 6:
        flash('新密码长度不能少于6个字符', 'danger')
        return redirect(url_for('user.profile'))
    
    # 更新密码
    current_user.password_hash = generate_password_hash(new_password)
    db.session.commit()
    
    flash('密码已成功更新', 'success')
    return redirect(url_for('user.profile'))

# 新增用户管理相关路由
@user_bp.route('/management')
@login_required
def management():
    """用户管理页面"""
    if not current_user.is_teacher():
        abort(403)  # 只有教师可以访问用户管理页面
    
    users = User.query.all()
    return render_template('user_management.html', users=users)

@user_bp.route('/create', methods=['GET', 'POST'])
@login_required
def create():
    """创建用户页面"""
    if not current_user.is_teacher():
        abort(403)
    
    if request.method == 'POST':
        username = request.form.get('username')
        name = request.form.get('name')
        email = request.form.get('email')
        password = request.form.get('password')
        user_type = request.form.get('user_type')
        
        # 验证表单数据
        if not username or not email or not password or not user_type:
            flash('所有带 * 的字段都必须填写', 'danger')
            return render_template('user_create.html')
        
        # 检查用户名和邮箱是否已存在
        if User.query.filter_by(username=username).first():
            flash('用户名已存在', 'danger')
            return render_template('user_create.html')
        
        if User.query.filter_by(email=email).first():
            flash('邮箱已被使用', 'danger')
            return render_template('user_create.html')
        
        # 如果用户名是admin，强制设置为教师类型
        if username == 'admin':
            user_type = 'teacher'
        
        # 创建新用户
        new_user = User(
            username=username,
            name=name,
            email=email,
            user_type=user_type
        )
        new_user.password = password
        
        db.session.add(new_user)
        db.session.commit()
        
        flash(f'用户 {username} 创建成功', 'success')
        return redirect(url_for('user.management'))
    
    return render_template('user_create.html')

@user_bp.route('/<int:user_id>/edit', methods=['GET', 'POST'])
@login_required
def edit(user_id):
    """编辑用户页面"""
    if not current_user.is_teacher():
        abort(403)
    
    user = User.query.get_or_404(user_id)
    
    if request.method == 'POST':
        name = request.form.get('name')
        email = request.form.get('email')
        user_type = request.form.get('user_type')
        is_active = 'is_active' in request.form
        new_password = request.form.get('new_password')
        
        # 检查邮箱是否已被其他用户使用
        if email != user.email and User.query.filter_by(email=email).first():
            flash('邮箱已被其他用户使用', 'danger')
            return render_template('user_edit.html', user=user)
        
        # 确保admin用户始终是教师类型
        if user.username == 'admin':
            user_type = 'teacher'
        
        # 更新用户信息
        user.name = name
        user.email = email
        user.user_type = user_type
        user.is_active = is_active
        
        # 如果提供了新密码则更新密码
        if new_password:
            if len(new_password) < 6:
                flash('密码长度不能少于6个字符', 'danger')
                return render_template('user_edit.html', user=user)
            user.password = new_password
        
        db.session.commit()
        flash('用户信息已更新', 'success')
        return redirect(url_for('user.management'))
    
    return render_template('user_edit.html', user=user)

@user_bp.route('/<int:user_id>/delete', methods=['POST'])
@login_required
def delete(user_id):
    """删除用户"""
    if not current_user.is_teacher():
        abort(403)
    
    if current_user.id == user_id:
        flash('不能删除自己的账号', 'danger')
        return redirect(url_for('user.management'))
    
    user = User.query.get_or_404(user_id)
    
    # 防止删除admin用户
    if user.username == 'admin':
        flash('不能删除管理员账号', 'danger')
        return redirect(url_for('user.management'))
    
    username = user.username
    
    db.session.delete(user)
    db.session.commit()
    
    flash(f'用户 {username} 已删除', 'success')
    return redirect(url_for('user.management'))
