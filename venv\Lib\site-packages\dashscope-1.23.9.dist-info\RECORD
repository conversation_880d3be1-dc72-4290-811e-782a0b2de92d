../../Scripts/dashscope.exe,sha256=cW9Bji7eBy6cvu-vZ_7yICTORXlLTAIdsmWF96UoN04,108410
dashscope-1.23.9.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
dashscope-1.23.9.dist-info/METADATA,sha256=tQQhkm5TxNwI2TCmWzT11dwWd9UGFb7cI1cojbo9Dpw,7123
dashscope-1.23.9.dist-info/RECORD,,
dashscope-1.23.9.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
dashscope-1.23.9.dist-info/WHEEL,sha256=_zCd3N1l69ArxyTb8rzEoP9TpbYXkqRFSNOD5OuxnTs,91
dashscope-1.23.9.dist-info/entry_points.txt,sha256=e9C3sOf9zDYL0O5ROEGX6FT8w-QK_kaGRWmPZDHAFys,49
dashscope-1.23.9.dist-info/licenses/LICENSE,sha256=xx0jnfkXJvxRnG63LTGOxlggYnIysveWIZ6H3PNdCrQ,11357
dashscope-1.23.9.dist-info/top_level.txt,sha256=woqavFJK9zas5xTqynmALqOtlafghjsk63Xk86powTU,10
dashscope/__init__.py,sha256=__BY0dzgFX5Bt50WL-2PJmI8EYOmnpXhABQntdqDtSM,3062
dashscope/__pycache__/__init__.cpython-311.pyc,,
dashscope/__pycache__/cli.cpython-311.pyc,,
dashscope/__pycache__/files.cpython-311.pyc,,
dashscope/__pycache__/model.cpython-311.pyc,,
dashscope/__pycache__/models.cpython-311.pyc,,
dashscope/__pycache__/version.cpython-311.pyc,,
dashscope/aigc/__init__.py,sha256=AuRhu_vA1K0tbs_C6DgcZYhTvxMuzDgpwHJNHzEPIHg,442
dashscope/aigc/__pycache__/__init__.cpython-311.pyc,,
dashscope/aigc/__pycache__/chat_completion.cpython-311.pyc,,
dashscope/aigc/__pycache__/code_generation.cpython-311.pyc,,
dashscope/aigc/__pycache__/conversation.cpython-311.pyc,,
dashscope/aigc/__pycache__/generation.cpython-311.pyc,,
dashscope/aigc/__pycache__/image_synthesis.cpython-311.pyc,,
dashscope/aigc/__pycache__/multimodal_conversation.cpython-311.pyc,,
dashscope/aigc/__pycache__/video_synthesis.cpython-311.pyc,,
dashscope/aigc/chat_completion.py,sha256=ONlyyssIbfaKKcFo7cEKhHx5OCF2XX810HFzIExW1ho,14813
dashscope/aigc/code_generation.py,sha256=p_mxDKJLQMW0IjFD46JRlZuEZCRESSVKEfLlAevBtqw,10936
dashscope/aigc/conversation.py,sha256=95xEEY4ThZJysj5zy3aMw7ql9KLJVfD_1iHv9QZ17Ew,14282
dashscope/aigc/generation.py,sha256=xMcMu16rICTdjZiD_sPqYV_Ltdp4ewGzzfC7JD9VApY,17948
dashscope/aigc/image_synthesis.py,sha256=Jgqmyv4jRxikgX7J18QrKKQ4OZAMxs6Q6YXObae3DhI,13363
dashscope/aigc/multimodal_conversation.py,sha256=1rZZRk_1lCdbVs7Rx1kJ5LvwWE1put5p_dQKdCX0ysY,5574
dashscope/aigc/video_synthesis.py,sha256=XQ3-NKYFmj5cIbUbLTbI0-FyC_fQp8eds6QmD1ZHj_0,13015
dashscope/api_entities/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
dashscope/api_entities/__pycache__/__init__.cpython-311.pyc,,
dashscope/api_entities/__pycache__/aiohttp_request.cpython-311.pyc,,
dashscope/api_entities/__pycache__/api_request_data.cpython-311.pyc,,
dashscope/api_entities/__pycache__/api_request_factory.cpython-311.pyc,,
dashscope/api_entities/__pycache__/base_request.cpython-311.pyc,,
dashscope/api_entities/__pycache__/chat_completion_types.cpython-311.pyc,,
dashscope/api_entities/__pycache__/dashscope_response.cpython-311.pyc,,
dashscope/api_entities/__pycache__/encryption.cpython-311.pyc,,
dashscope/api_entities/__pycache__/http_request.cpython-311.pyc,,
dashscope/api_entities/__pycache__/websocket_request.cpython-311.pyc,,
dashscope/api_entities/aiohttp_request.py,sha256=1L7XdIJ9L65cQmX8x9JCR4t5hNIMDrbiWADfKKp9yfo,10280
dashscope/api_entities/api_request_data.py,sha256=04rpYPNK1HkT3iTPJmZpquH621xcBbe8R8EGrDJSLt0,5514
dashscope/api_entities/api_request_factory.py,sha256=18A40aHL0t3s01VdbkIWRGNeVJyX0GXRHTZUxau7po4,5640
dashscope/api_entities/base_request.py,sha256=W2SzrSAGFS6V8DErfSrayQtSL0T4iO7BrC8flr7nt1w,977
dashscope/api_entities/chat_completion_types.py,sha256=1WMWPszhM3HaJBVz-ZXx-El4D8-RfVUL3ym65xsDRLk,11435
dashscope/api_entities/dashscope_response.py,sha256=qNNB86h5Gb_4uHjBD_4lx6UckyQzSdaTgjze1prc12M,22073
dashscope/api_entities/encryption.py,sha256=rUCZx3wwVvS5oyKXEeWgyWPxM8Y5d4AaVdgxLhizBqA,5517
dashscope/api_entities/http_request.py,sha256=p2xfmq79evNON4ctCVXCcrJo8jnKABn0XzdTkTDgbLM,14540
dashscope/api_entities/websocket_request.py,sha256=PS0FU854-HjTbKa68f4GHa7-noFRMzKySJGfPkrrBjw,16146
dashscope/app/__init__.py,sha256=xvSvU8O7m5u7vgIvJXTJektJZxmjT2Rpt_YwePH88XE,113
dashscope/app/__pycache__/__init__.cpython-311.pyc,,
dashscope/app/__pycache__/application.cpython-311.pyc,,
dashscope/app/__pycache__/application_response.cpython-311.pyc,,
dashscope/app/application.py,sha256=Whf_ij4RHOaY12_xdS8uj8HVNCwkTp_MRdrFTryF1Kg,9472
dashscope/app/application_response.py,sha256=z9BKcb9QuV_TzHkqtwaCwap2GQ1mP48uDD7gIc2il98,7038
dashscope/assistants/__init__.py,sha256=hjCTuv13yFaXyUqlexAU-RaO0Ahq3P7VK9_LkSbkGVU,434
dashscope/assistants/__pycache__/__init__.cpython-311.pyc,,
dashscope/assistants/__pycache__/assistant_types.cpython-311.pyc,,
dashscope/assistants/__pycache__/assistants.cpython-311.pyc,,
dashscope/assistants/__pycache__/files.cpython-311.pyc,,
dashscope/assistants/assistant_types.py,sha256=DQ_lOust10wjiV38Nlsu3HaGYNbGlJoaLjEza82Wotk,4262
dashscope/assistants/assistants.py,sha256=t8BGu9K2YbNpBenUyFFDMx5WpZ5x5OtbssJkj0xfVo0,10930
dashscope/assistants/files.py,sha256=Ol2h7L2vNV8kgWqum2B-3B9vtLEHB_2KWt0K7e96Bmg,6750
dashscope/audio/__init__.py,sha256=7e3ejVsDJxEbMHN-9E0nEDfU-CnnQ4JgtgUxqNs0IG4,192
dashscope/audio/__pycache__/__init__.cpython-311.pyc,,
dashscope/audio/asr/__init__.py,sha256=JoCenJAUVOQXPmAn1toKeFYCfc8BqNn0NKpqjuJvNJc,1055
dashscope/audio/asr/__pycache__/__init__.cpython-311.pyc,,
dashscope/audio/asr/__pycache__/asr_phrase_manager.cpython-311.pyc,,
dashscope/audio/asr/__pycache__/recognition.cpython-311.pyc,,
dashscope/audio/asr/__pycache__/transcription.cpython-311.pyc,,
dashscope/audio/asr/__pycache__/translation_recognizer.cpython-311.pyc,,
dashscope/audio/asr/__pycache__/vocabulary.cpython-311.pyc,,
dashscope/audio/asr/asr_phrase_manager.py,sha256=vHOLExaKCtjedkihIu7gyfQyarR9rN5JZn79LvlCpco,7693
dashscope/audio/asr/recognition.py,sha256=b_aAPvOKjpWdSiYhM_hp30sZ06QdmNBSDJwhiv78kHM,20932
dashscope/audio/asr/transcription.py,sha256=lYzPjh7jJQwjMoxx8-AY0YCMBKNKO0bi7xd5tZGSHPc,9094
dashscope/audio/asr/translation_recognizer.py,sha256=JgBmhkIl_kqH8uVwop6Fba5KlXccftKFrhaygN9PKjU,39680
dashscope/audio/asr/vocabulary.py,sha256=N0pMS2x1lDxqJ14FgTGKctfuVkR2_hlEsCNWFcgYpTY,6717
dashscope/audio/qwen_omni/__init__.py,sha256=MEFxmyxr5H6bW22l_R9073Pl6Ka6knvhrATGT-4UBjI,298
dashscope/audio/qwen_omni/__pycache__/__init__.cpython-311.pyc,,
dashscope/audio/qwen_omni/__pycache__/omni_realtime.cpython-311.pyc,,
dashscope/audio/qwen_omni/omni_realtime.py,sha256=eBmoOxuKcfzMHuXsQWCrIIKmso9iEzYylOeYZ5upv-w,14869
dashscope/audio/qwen_tts/__init__.py,sha256=JS3axY1grqO0aTIJufZ3KS1JsU6yf6y4K2CQlNvUK9I,132
dashscope/audio/qwen_tts/__pycache__/__init__.cpython-311.pyc,,
dashscope/audio/qwen_tts/__pycache__/speech_synthesizer.cpython-311.pyc,,
dashscope/audio/qwen_tts/speech_synthesizer.py,sha256=7LHR-PXhn-VE1cCOp_82Jq0zE9rMc3xy3dszUeyLLNs,2927
dashscope/audio/qwen_tts_realtime/__init__.py,sha256=vVkmeJr_mEAn_O0Rh5AU3ICg6qIZqppUryJ5lY8VYPo,254
dashscope/audio/qwen_tts_realtime/__pycache__/__init__.cpython-311.pyc,,
dashscope/audio/qwen_tts_realtime/__pycache__/qwen_tts_realtime.cpython-311.pyc,,
dashscope/audio/qwen_tts_realtime/qwen_tts_realtime.py,sha256=8bOAMcDasTHwSLb9xAGJoj9eUPpQHh2aWvonV6Kf1U4,10367
dashscope/audio/tts/__init__.py,sha256=xYpMFseUZGgqgj_70zcX2VsLv-L7qxJ3d-bbdj_hO0I,245
dashscope/audio/tts/__pycache__/__init__.cpython-311.pyc,,
dashscope/audio/tts/__pycache__/speech_synthesizer.cpython-311.pyc,,
dashscope/audio/tts/speech_synthesizer.py,sha256=vD1xQV-rew8qAsIaAGH5amsNtB0SqdtNhVHhJHGQ-xk,7622
dashscope/audio/tts_v2/__init__.py,sha256=me9a3_7KsHQxcJ8hx4SeKlY1e_ThHVvGMw7Yn0uoscM,333
dashscope/audio/tts_v2/__pycache__/__init__.cpython-311.pyc,,
dashscope/audio/tts_v2/__pycache__/enrollment.cpython-311.pyc,,
dashscope/audio/tts_v2/__pycache__/speech_synthesizer.cpython-311.pyc,,
dashscope/audio/tts_v2/enrollment.py,sha256=-nrlywYSOP73Bm9ETTSxNnlp-B8ezJcUmd59mVvyvgk,6361
dashscope/audio/tts_v2/speech_synthesizer.py,sha256=qUoLga8HpvNVdbN5n_orxrgZ28yD6Lhwuwqeoi1T7yA,20056
dashscope/cli.py,sha256=amegoTkGOs6TlHMdoo4JVOqBePo3lGs745rc7leEyrE,24020
dashscope/client/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
dashscope/client/__pycache__/__init__.cpython-311.pyc,,
dashscope/client/__pycache__/base_api.cpython-311.pyc,,
dashscope/client/base_api.py,sha256=aWNy_xm02GXuLKVgWnYJht2nI4ZHSGfYIcr52SML15A,41239
dashscope/common/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
dashscope/common/__pycache__/__init__.cpython-311.pyc,,
dashscope/common/__pycache__/api_key.cpython-311.pyc,,
dashscope/common/__pycache__/base_type.cpython-311.pyc,,
dashscope/common/__pycache__/constants.cpython-311.pyc,,
dashscope/common/__pycache__/env.cpython-311.pyc,,
dashscope/common/__pycache__/error.cpython-311.pyc,,
dashscope/common/__pycache__/logging.cpython-311.pyc,,
dashscope/common/__pycache__/message_manager.cpython-311.pyc,,
dashscope/common/__pycache__/utils.cpython-311.pyc,,
dashscope/common/api_key.py,sha256=yqFCAteq8CNQGnlLv6fxNFWsLqsQDbSzOpgAlUmDkaE,2037
dashscope/common/base_type.py,sha256=2OQDqFlEH43wn54i-691cbarV_eKRLvRsPGfyb_GS0g,4670
dashscope/common/constants.py,sha256=Ry3IBz2w9amRoRmfwC5L1dXHb7Iz2slj_I_wobmo-6Q,2446
dashscope/common/env.py,sha256=9yWWdKqfYuHlTQSvbTBaQhGbASh5Lq6SbM9pPx8hB40,920
dashscope/common/error.py,sha256=sXQqBGWCUBPyKa5rAI6DWc0sEidH01sR8zlIBfrTTDU,2690
dashscope/common/logging.py,sha256=lX86X9ND1MC5mA_qKAktwaVXd_BufLgmSGPggUiEJZo,1035
dashscope/common/message_manager.py,sha256=mZ7fS5LV09huwvz-2nxrr2RFQ9fQAYhEpeUmFb7WfW4,1148
dashscope/common/utils.py,sha256=i0pnYxz5zErw1wM-eFQx2XLfOmBjFscqG4-aEJYnlRc,15439
dashscope/customize/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
dashscope/customize/__pycache__/__init__.cpython-311.pyc,,
dashscope/customize/__pycache__/customize_types.cpython-311.pyc,,
dashscope/customize/__pycache__/deployments.cpython-311.pyc,,
dashscope/customize/__pycache__/finetunes.cpython-311.pyc,,
dashscope/customize/customize_types.py,sha256=02qxJ-FodKS9Sgl7blx7IAx_eOdpf53L1mZ909PSMsM,4854
dashscope/customize/deployments.py,sha256=2BxjgukuXe9bkl1VOvvKky0NxkcXVL3xk07UusjwZII,5240
dashscope/customize/finetunes.py,sha256=AL_kGTJXMvM2ej-EKsLLd1dUphPQdVTefFVCSVH-C-w,8362
dashscope/embeddings/__init__.py,sha256=XQ7vKr8oZM2CmdOduE53BWy6_Qpn9xUPkma64yw8Gws,291
dashscope/embeddings/__pycache__/__init__.cpython-311.pyc,,
dashscope/embeddings/__pycache__/batch_text_embedding.cpython-311.pyc,,
dashscope/embeddings/__pycache__/batch_text_embedding_response.cpython-311.pyc,,
dashscope/embeddings/__pycache__/multimodal_embedding.cpython-311.pyc,,
dashscope/embeddings/__pycache__/text_embedding.cpython-311.pyc,,
dashscope/embeddings/batch_text_embedding.py,sha256=lVhvTS8McYfXuqt_8CmmhA6bPqD0nrGv965kjYG_j0E,8842
dashscope/embeddings/batch_text_embedding_response.py,sha256=ZfkJMUq8GRsFA6XUTsiAsIySqGJH-VPi2P9Ba1KTU-s,2056
dashscope/embeddings/multimodal_embedding.py,sha256=NwjQsdkKgUz51ozGjqFDzVlLcZjY0m1JNdH1EyAY0a4,4109
dashscope/embeddings/text_embedding.py,sha256=2MPEyMB99xueDbvFg9kKAe8bgHMDEaFLaFa6GzDWDHg,2108
dashscope/files.py,sha256=vRDQygm3lOqBZR73o7KNHs1iTBVuvLncuwJNxIYjzAU,3981
dashscope/io/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
dashscope/io/__pycache__/__init__.cpython-311.pyc,,
dashscope/io/__pycache__/input_output.cpython-311.pyc,,
dashscope/io/input_output.py,sha256=0aXrRJFo1ZqYm_AJWR_w88O4-Btn9np2zUhrrUdBdfw,3992
dashscope/model.py,sha256=B5v_BtYLPqj6raClejBgdKg6WTGwhH_f-20pvsQqmsk,1491
dashscope/models.py,sha256=dE4mzXkl85G343qVylSGpURPRdA5pZSqXlx6PcxqC_Q,1275
dashscope/multimodal/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
dashscope/multimodal/__pycache__/__init__.cpython-311.pyc,,
dashscope/multimodal/__pycache__/dialog_state.cpython-311.pyc,,
dashscope/multimodal/__pycache__/multimodal_constants.cpython-311.pyc,,
dashscope/multimodal/__pycache__/multimodal_dialog.cpython-311.pyc,,
dashscope/multimodal/__pycache__/multimodal_request_params.cpython-311.pyc,,
dashscope/multimodal/dialog_state.py,sha256=CtOdfGWhq0ePG3bc8-7inhespETtPD4QDli1513hd1A,1522
dashscope/multimodal/multimodal_constants.py,sha256=z_QVq01E43FAqKQnDu9vdf89d1zuYlWyANewWTEXVJM,1282
dashscope/multimodal/multimodal_dialog.py,sha256=HymlaQYp7SgJdoKbT27SNiviyRRoM91zklNBwTHmm1Q,23939
dashscope/multimodal/multimodal_request_params.py,sha256=9Dlvyy0u67K5FtMfWkCRLHKsevTM8jvT2V-OljZP5sM,8350
dashscope/nlp/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
dashscope/nlp/__pycache__/__init__.cpython-311.pyc,,
dashscope/nlp/__pycache__/understanding.cpython-311.pyc,,
dashscope/nlp/understanding.py,sha256=00ado-ibYEzBRT0DgKGd3bohQDNW73xnFhJ_1aa87lw,2880
dashscope/protocol/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
dashscope/protocol/__pycache__/__init__.cpython-311.pyc,,
dashscope/protocol/__pycache__/websocket.cpython-311.pyc,,
dashscope/protocol/websocket.py,sha256=k4B8GOBeyvAxqVQ47JhWfXfNErIhiVlQ-VCiKLLG0Ho,613
dashscope/rerank/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
dashscope/rerank/__pycache__/__init__.cpython-311.pyc,,
dashscope/rerank/__pycache__/text_rerank.cpython-311.pyc,,
dashscope/rerank/text_rerank.py,sha256=NKN3vnWprguhHy2_g0D7znZ7jEGrLX4zMaLE3jBrl94,2449
dashscope/resources/qwen.tiktoken,sha256=srG437XMXwJLr8NzEhxquj9m-aWgJp4kNHCh3hajMYY,2561218
dashscope/threads/__init__.py,sha256=3IKX9vZWhT87XrVx1pA_g3MWHEekXoJJSZeE_CTWL08,672
dashscope/threads/__pycache__/__init__.cpython-311.pyc,,
dashscope/threads/__pycache__/thread_types.cpython-311.pyc,,
dashscope/threads/__pycache__/threads.cpython-311.pyc,,
dashscope/threads/messages/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
dashscope/threads/messages/__pycache__/__init__.cpython-311.pyc,,
dashscope/threads/messages/__pycache__/files.cpython-311.pyc,,
dashscope/threads/messages/__pycache__/messages.cpython-311.pyc,,
dashscope/threads/messages/files.py,sha256=WxKVQednISIh2MY8N1B6Y4HjGllFhcLKCsc4QXKZ6AQ,3871
dashscope/threads/messages/messages.py,sha256=peKqehK8JO0ZwRXACaojg6-61TkBBbqd190xtKIbOZo,8470
dashscope/threads/runs/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
dashscope/threads/runs/__pycache__/__init__.cpython-311.pyc,,
dashscope/threads/runs/__pycache__/runs.cpython-311.pyc,,
dashscope/threads/runs/__pycache__/steps.cpython-311.pyc,,
dashscope/threads/runs/runs.py,sha256=KHBfdBYwIV-3pZG8RjZm5qZK-UMQT3cttV8kEfyH50M,18600
dashscope/threads/runs/steps.py,sha256=579EsCOwsamuJMSNrrrX86h9JfMdXNlErVJ8XakSqSc,3689
dashscope/threads/thread_types.py,sha256=1_CCRrfDL2tFE5VMj2tjIdZ2N7pvbNffkMF97Fuwzcg,18331
dashscope/threads/threads.py,sha256=J9QGY0vy6MldC4ujQMyiYc9jN4aH9NGj0SkcWZHwkj0,7716
dashscope/tokenizers/__init__.py,sha256=TvVAsDam5S0R4rorxdfyUGIEQQX1q8nQ--RxsWWos3A,251
dashscope/tokenizers/__pycache__/__init__.cpython-311.pyc,,
dashscope/tokenizers/__pycache__/qwen_tokenizer.cpython-311.pyc,,
dashscope/tokenizers/__pycache__/tokenization.cpython-311.pyc,,
dashscope/tokenizers/__pycache__/tokenizer.cpython-311.pyc,,
dashscope/tokenizers/__pycache__/tokenizer_base.cpython-311.pyc,,
dashscope/tokenizers/qwen_tokenizer.py,sha256=tvX7x34Rg_NFFc1XjneXNFfXVkePdqkgHHShce2RJGo,4162
dashscope/tokenizers/tokenization.py,sha256=ubQBJ_yw_MoHuHxZcK9NarZSSbyExloeSOLIWYhRzH0,4824
dashscope/tokenizers/tokenizer.py,sha256=3FQVDvMNkCW9ccYeJdjrd_PIMMD3Xv7aNZkaYOE4XX4,1205
dashscope/tokenizers/tokenizer_base.py,sha256=5EJIFuizMWESEmLmbd38yJnfeHmPnzZPwsO4aOGjpl4,707
dashscope/utils/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
dashscope/utils/__pycache__/__init__.cpython-311.pyc,,
dashscope/utils/__pycache__/oss_utils.cpython-311.pyc,,
dashscope/utils/oss_utils.py,sha256=L5LN3lN8etVxSL_jkZydstvEKpnTG9CY0zcvPGQ5LBo,7383
dashscope/version.py,sha256=ACHUqhomdzZ7aih_Y368NUmXBBfj2GY5GqSf2NoTta4,74
