from datetime import datetime
from . import db

class PromptCategory(db.Model):
    """提示词分类模型"""
    __tablename__ = 'prompt_categories'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(64), unique=True, index=True)
    description = db.Column(db.String(255))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # 关系
    prompts = db.relationship('Prompt', backref='category', lazy='dynamic')
    
    def __repr__(self):
        return f'<PromptCategory {self.name}>'

class Prompt(db.Model):
    """提示词模型"""
    __tablename__ = 'prompts'
    
    id = db.Column(db.Integer, primary_key=True)
    title = db.Column(db.String(128), index=True)
    content = db.Column(db.Text)
    category_id = db.Column(db.<PERSON>teger, db.<PERSON><PERSON><PERSON>('prompt_categories.id'))
    created_by = db.Column(db.Integer, db.<PERSON>ey('users.id'))
    tags = db.Column(db.String(255))
    is_public = db.Column(db.Boolean, default=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    def __repr__(self):
        return f'<Prompt {self.title}>'
