{% extends "base.html" %}

{% block title %}提示词库 - 智慧成本核算教学系统{% endblock %}

{% block extra_css %}
<style>
    .category-header {
        background-color: #f8f9fa;
        padding: 10px 15px;
        margin-bottom: 15px;
        border-radius: 5px;
        border-left: 4px solid #007bff;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    
    .category-title {
        font-size: 1.2rem;
        font-weight: 600;
        margin-bottom: 0;
    }
    
    .prompt-table {
        margin-bottom: 30px;
    }
    
    .prompt-content {
        max-height: 100px;
        overflow-y: auto;
        background-color: #f8f9fa;
        padding: 8px;
        border-radius: 5px;
        font-family: monospace;
    }
    
    .prompt-tags {
        display: flex;
        flex-wrap: wrap;
        gap: 5px;
    }
    
    .prompt-tag {
        background-color: #e9ecef;
        padding: 2px 8px;
        border-radius: 15px;
        font-size: 0.8rem;
        display: inline-block;
    }
    
    .category-filter {
        margin-bottom: 20px;
    }
    
    .category-badge {
        cursor: pointer;
        padding: 5px 10px;
        margin-right: 5px;
        margin-bottom: 5px;
        border-radius: 20px;
        background-color: #e9ecef;
        transition: all 0.2s ease;
        display: inline-block;
        font-size: 0.9rem;
        border: 1px solid transparent;
    }
    
    .category-badge:hover {
        background-color: #dee2e6;
        transform: translateY(-2px);
    }
    
    .category-badge.active {
        background-color: #007bff;
        color: white;
        box-shadow: 0 2px 5px rgba(0, 123, 255, 0.3);
    }
    
    .public-badge {
        background-color: #28a745;
    }
    
    .private-badge {
        background-color: #dc3545;
    }
    
    /* 分类管理样式 */
    .category-item {
        background-color: #f8f9fa;
        border-radius: 8px;
        margin-bottom: 15px;
        padding: 15px;
        border-left: 4px solid #007bff;
        transition: all 0.2s ease;
    }
    
    .category-item:hover {
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        transform: translateY(-2px);
    }
    
    .category-management-header {
        background-color: #f8f9fa;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 20px;
    }
    
    .category-table {
        box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        border-radius: 8px;
        overflow: hidden;
    }
    
    .category-table th {
        background-color: #f1f3f5;
        border-top: none;
    }
    
    .btn-category-action {
        width: 32px;
        height: 32px;
        padding: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 4px;
        margin-right: 5px;
    }
    
    .category-form-container {
        background-color: #f8f9fa;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 20px;
    }
    
    /* 动画效果 */
    .fade-in {
        animation: fadeIn 0.3s ease-in;
    }
    
    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(10px); }
        to { opacity: 1; transform: translateY(0); }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="row mb-4">
        <div class="col-md-8">
            <h2>提示词库</h2>
            <p class="text-muted">选择合适的提示词模板，快速开始对话</p>
        </div>
        <div class="col-md-4 text-end">
            {% if current_user.is_teacher() %}
            <button type="button" class="btn btn-outline-primary" data-bs-toggle="modal" data-bs-target="#categoryModal">
                <i class="fas fa-folder-plus me-2"></i>分类管理
            </button>
            <a href="{{ url_for('prompt.create') }}" class="btn btn-primary ms-2">
                <i class="fas fa-plus me-2"></i>创建提示词
            </a>
            {% endif %}
        </div>
    </div>

    <!-- 分类过滤器 -->
    <div class="category-filter mb-4">
        <div class="card">
            <div class="card-body">
                <h6 class="mb-3"><i class="fas fa-filter me-2"></i>按分类筛选</h6>
                <div>
                    <span class="category-badge active" data-category="all">
                        <i class="fas fa-layer-group me-1"></i>全部
                    </span>
                    {% for category in categories %}
                    <span class="category-badge" data-category="{{ category.id }}">
                        <i class="fas fa-folder me-1"></i>{{ category.name }}
                    </span>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
    
    <!-- 提示词列表（按分类分组） -->
    <div id="promptsContainer">
        {% for category in categories %}
        <div class="category-section mb-4" data-category-id="{{ category.id }}">
            <div class="category-header">
                <h5 class="category-title">{{ category.name }}</h5>
            </div>
            
            <div class="table-responsive">
                <table class="table table-striped table-hover prompt-table">
                    <thead>
                        <tr>
                            <th width="5%">#</th>
                            <th width="15%">标题</th>
                            <th width="40%">内容</th>
                            <th width="15%">标签</th>
                            <th width="10%">状态</th>
                            <th width="15%">操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% set prompts_in_category = prompts|selectattr('category_id', 'equalto', category.id)|list %}
                        {% if prompts_in_category %}
                            {% for prompt in prompts_in_category %}
                            <tr>
                                <td>{{ loop.index }}</td>
                                <td>{{ prompt.title }}</td>
                                <td>
                                    <div class="prompt-content">{{ prompt.content }}</div>
                                </td>
                                <td>
                                    <div class="prompt-tags">
                                        {% if prompt.tags %}
                                            {% for tag in prompt.tags.split(',') %}
                                            <span class="prompt-tag">{{ tag }}</span>
                                            {% endfor %}
                                        {% endif %}
                                    </div>
                                </td>
                                <td>
                                    <span class="badge {% if prompt.is_public %}public-badge{% else %}private-badge{% endif %}">
                                        {{ "公开" if prompt.is_public else "私有" }}
                                    </span>
                                </td>
                                <td>
                                    <div class="btn-group">
                                        <button class="btn btn-sm btn-primary use-prompt-btn" data-prompt-id="{{ prompt.id }}" title="使用">
                                            <i class="fas fa-comment-dots"></i>
                                        </button>
                                        {% if current_user.is_teacher() or prompt.created_by == current_user.id %}
                                        <a href="{{ url_for('prompt.edit', prompt_id=prompt.id) }}" class="btn btn-sm btn-outline-primary" title="编辑">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <button type="button" class="btn btn-sm btn-outline-danger delete-prompt-btn" 
                                                data-prompt-id="{{ prompt.id }}" 
                                                data-prompt-title="{{ prompt.title }}" title="删除">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        {% else %}
                            <tr>
                                <td colspan="6" class="text-center py-3">
                                    <p class="text-muted mb-0">该分类下暂无提示词</p>
                                </td>
                            </tr>
                        {% endif %}
                    </tbody>
                </table>
            </div>
        </div>
        {% endfor %}
        
        {% if not categories %}
        <div class="text-center py-5">
            <i class="fas fa-folder-open fa-3x text-muted mb-3"></i>
            <h4>暂无分类</h4>
            <p class="text-muted">请先创建分类，然后添加提示词</p>
        </div>
        {% endif %}
    </div>
</div>

<!-- 查看提示词模态框 -->
<div class="modal fade" id="viewPromptModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="view-prompt-title"></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3" id="view-prompt-content"></div>
                <div class="mb-3">
                    <small class="text-muted" id="view-prompt-meta"></small>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-primary" id="use-prompt-btn">使用此提示词</button>
            </div>
        </div>
    </div>
</div>

<!-- 删除确认模态框 -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">确认删除</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>确定要删除提示词 <span id="deletePromptTitle" class="fw-bold"></span> 吗？此操作不可撤销。</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <form id="deleteForm" method="POST">
                    <button type="submit" class="btn btn-danger">删除</button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- 分类管理模态框 -->
<div class="modal fade" id="categoryModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-light">
                <h5 class="modal-title"><i class="fas fa-folder me-2"></i>分类管理</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="category-management-header d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="mb-1"><i class="fas fa-list-ul me-2"></i>现有分类</h6>
                        <p class="text-muted small mb-0">管理提示词分类，便于组织和查找</p>
                    </div>
                    <button type="button" class="btn btn-primary" id="addCategoryBtn">
                        <i class="fas fa-plus me-1"></i> 新增分类
                    </button>
                </div>
                
                <div class="table-responsive">
                    <table class="table table-striped table-hover category-table">
                        <thead>
                            <tr>
                                <th width="5%">#</th>
                                <th width="25%">分类名称</th>
                                <th width="50%">描述</th>
                                <th width="20%">操作</th>
                            </tr>
                        </thead>
                        <tbody id="categoriesTableBody" class="fade-in">
                            {% for category in categories %}
                            <tr>
                                <td>{{ loop.index }}</td>
                                <td><span class="fw-medium">{{ category.name }}</span></td>
                                <td>{{ category.description }}</td>
                                <td>
                                    <div class="d-flex">
                                        <button type="button" class="btn btn-outline-primary btn-category-action edit-category-btn" 
                                                data-category-id="{{ category.id }}" 
                                                data-category-name="{{ category.name }}" 
                                                data-category-desc="{{ category.description }}"
                                                title="编辑分类">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button type="button" class="btn btn-outline-danger btn-category-action delete-category-btn" 
                                                data-category-id="{{ category.id }}" 
                                                data-category-name="{{ category.name }}"
                                                title="删除分类">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>

<!-- 分类编辑模态框 -->
<div class="modal fade" id="editCategoryModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-light">
                <h5 class="modal-title" id="categoryModalTitle"><i class="fas fa-edit me-2"></i>编辑分类</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="category-form-container fade-in">
                    <form id="categoryForm">
                        <input type="hidden" id="categoryId" value="">
                        <div class="mb-3">
                            <label for="categoryName" class="form-label">分类名称</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-tag"></i></span>
                                <input type="text" class="form-control" id="categoryName" placeholder="输入分类名称" required>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="categoryDescription" class="form-label">分类描述</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-info-circle"></i></span>
                                <textarea class="form-control" id="categoryDescription" rows="3" placeholder="输入分类描述（可选）"></textarea>
                            </div>
                            <div class="form-text">简要描述该分类的用途和包含内容</div>
                        </div>
                    </form>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-1"></i> 取消
                </button>
                <button type="button" class="btn btn-primary" id="saveCategoryBtn">
                    <i class="fas fa-save me-1"></i> 保存
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 删除分类确认模态框 -->
<div class="modal fade" id="deleteCategoryModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title"><i class="fas fa-exclamation-triangle me-2"></i>确认删除分类</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body fade-in">
                <div class="text-center mb-4">
                    <i class="fas fa-trash-alt text-danger fa-3x mb-3"></i>
                    <h5>确定要删除分类 <span id="deleteCategoryName" class="fw-bold"></span> 吗？</h5>
                    <p class="text-muted">此操作不可撤销</p>
                </div>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-circle me-2"></i>
                    <strong>注意：</strong>如果该分类下存在提示词，将无法删除。请先移动或删除该分类下的所有提示词。
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-1"></i> 取消
                </button>
                <button type="button" class="btn btn-danger" id="confirmDeleteCategoryBtn">
                    <i class="fas fa-trash-alt me-1"></i> 确认删除
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 分类过滤
        const categoryBadges = document.querySelectorAll('.category-badge');
        const categorySections = document.querySelectorAll('.category-section');
        
        categoryBadges.forEach(badge => {
            badge.addEventListener('click', function() {
                // 更新激活状态
                categoryBadges.forEach(b => b.classList.remove('active'));
                this.classList.add('active');
                
                const category = this.getAttribute('data-category');
                
                // 过滤分类
                categorySections.forEach(section => {
                    if (category === 'all' || section.getAttribute('data-category-id') === category) {
                        section.style.display = 'block';
                    } else {
                        section.style.display = 'none';
                    }
                });
            });
        });
        
        // 使用提示词
        const usePromptBtns = document.querySelectorAll('.use-prompt-btn');
        usePromptBtns.forEach(btn => {
            btn.addEventListener('click', function() {
                const promptId = this.getAttribute('data-prompt-id');
                // 创建新会话并使用此提示词
                window.location.href = `/chat/create?prompt_id=${promptId}`;
            });
        });
        
        // 删除提示词
        const deletePromptBtns = document.querySelectorAll('.delete-prompt-btn');
        const deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
        
        deletePromptBtns.forEach(btn => {
            btn.addEventListener('click', function() {
                const promptId = this.getAttribute('data-prompt-id');
                const promptTitle = this.getAttribute('data-prompt-title');
                
                document.getElementById('deletePromptTitle').textContent = promptTitle;
                document.getElementById('deleteForm').action = `/prompt/${promptId}/delete`;
                deleteModal.show();
            });
        });
        
        // 查看提示词详情
        const viewPromptModal = document.getElementById('viewPromptModal');
        if (viewPromptModal) {
            viewPromptModal.addEventListener('show.bs.modal', function(event) {
                const button = event.relatedTarget;
                const promptId = button.getAttribute('data-prompt-id');
                
                // 这里应该发送AJAX请求获取提示词详情
                // 为了演示，这里使用模拟数据
                document.getElementById('view-prompt-title').textContent = button.closest('tr').querySelector('td:nth-child(2)').textContent;
                document.getElementById('view-prompt-content').textContent = button.closest('tr').querySelector('.prompt-content').textContent;
                document.getElementById('view-prompt-meta').textContent = '分类：' + button.closest('.category-section').querySelector('.category-title').textContent;
                
                document.getElementById('use-prompt-btn').setAttribute('data-prompt-id', promptId);
            });
            
            document.getElementById('use-prompt-btn').addEventListener('click', function() {
                const promptId = this.getAttribute('data-prompt-id');
                window.location.href = `/chat/create?prompt_id=${promptId}`;
            });
        }
        
        // 分类管理相关功能
        const categoryModal = new bootstrap.Modal(document.getElementById('categoryModal'));
        const editCategoryModal = new bootstrap.Modal(document.getElementById('editCategoryModal'));
        const deleteCategoryModal = new bootstrap.Modal(document.getElementById('deleteCategoryModal'));
        
        // 新增分类按钮点击事件
        document.getElementById('addCategoryBtn').addEventListener('click', function() {
            document.getElementById('categoryModalTitle').textContent = '新增分类';
            document.getElementById('categoryId').value = '';
            document.getElementById('categoryName').value = '';
            document.getElementById('categoryDescription').value = '';
            
            categoryModal.hide();
            editCategoryModal.show();
        });
        
        // 编辑分类按钮点击事件
        const editCategoryBtns = document.querySelectorAll('.edit-category-btn');
        editCategoryBtns.forEach(btn => {
            btn.addEventListener('click', function() {
                const categoryId = this.getAttribute('data-category-id');
                const categoryName = this.getAttribute('data-category-name');
                const categoryDesc = this.getAttribute('data-category-desc');
                
                document.getElementById('categoryModalTitle').textContent = '编辑分类';
                document.getElementById('categoryId').value = categoryId;
                document.getElementById('categoryName').value = categoryName;
                document.getElementById('categoryDescription').value = categoryDesc;
                
                categoryModal.hide();
                editCategoryModal.show();
            });
        });
        
        // 保存分类按钮点击事件
        document.getElementById('saveCategoryBtn').addEventListener('click', function() {
            const categoryId = document.getElementById('categoryId').value;
            const categoryName = document.getElementById('categoryName').value;
            const categoryDescription = document.getElementById('categoryDescription').value;
            
            if (!categoryName.trim()) {
                alert('分类名称不能为空');
                return;
            }
            
            const formData = new FormData();
            formData.append('name', categoryName);
            formData.append('description', categoryDescription);
            
            const url = categoryId ? `/prompt/category/${categoryId}/edit` : '/prompt/category/create';
            
            fetch(url, {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // 刷新页面
                    window.location.reload();
                } else {
                    alert('操作失败: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('操作失败，请重试');
            });
        });
        
        // 删除分类按钮点击事件
        const deleteCategoryBtns = document.querySelectorAll('.delete-category-btn');
        deleteCategoryBtns.forEach(btn => {
            btn.addEventListener('click', function() {
                const categoryId = this.getAttribute('data-category-id');
                const categoryName = this.getAttribute('data-category-name');
                
                document.getElementById('deleteCategoryName').textContent = categoryName;
                document.getElementById('confirmDeleteCategoryBtn').setAttribute('data-category-id', categoryId);
                
                categoryModal.hide();
                deleteCategoryModal.show();
            });
        });
        
        // 确认删除分类按钮点击事件
        document.getElementById('confirmDeleteCategoryBtn').addEventListener('click', function() {
            const categoryId = this.getAttribute('data-category-id');
            
            fetch(`/prompt/category/${categoryId}/delete`, {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // 刷新页面
                    window.location.reload();
                } else {
                    alert('删除失败: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('删除失败，请重试');
            });
        });
    });
</script>
{% endblock %} 