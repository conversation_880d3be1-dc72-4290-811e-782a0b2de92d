{% extends "base.html" %}

{% block title %}个人资料 - 智慧成本核算教学系统{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-4">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">个人资料</h5>
            </div>
            <div class="card-body">
                <div class="text-center mb-4">
                    <div class="avatar-circle mb-3">
                        <span class="avatar-initials">{{ user.name[0]|upper if user.name else user.username[0]|upper }}</span>
                    </div>
                    <h4>{{ user.name or user.username }}</h4>
                    <p class="text-muted">
                        {% if user.is_teacher() %}
                        <span class="badge bg-primary">教师</span>
                        {% else %}
                        <span class="badge bg-secondary">学生</span>
                        {% endif %}
                    </p>
                </div>
                
                <ul class="list-group list-group-flush">
                    <li class="list-group-item d-flex justify-content-between">
                        <span class="text-muted">用户名</span>
                        <span>{{ user.username }}</span>
                    </li>
                    <li class="list-group-item d-flex justify-content-between">
                        <span class="text-muted">邮箱</span>
                        <span>{{ user.email }}</span>
                    </li>
                    <li class="list-group-item d-flex justify-content-between">
                        <span class="text-muted">注册时间</span>
                        <span>{{ user.created_at.strftime('%Y-%m-%d') }}</span>
                    </li>
                    <li class="list-group-item d-flex justify-content-between">
                        <span class="text-muted">最后登录</span>
                        <span>{{ user.last_login.strftime('%Y-%m-%d %H:%M') if user.last_login else '未知' }}</span>
                    </li>
                </ul>
            </div>
        </div>
    </div>
    
    <div class="col-md-8">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">修改个人资料</h5>
            </div>
            <div class="card-body">
                <form action="{{ url_for('user.update_profile') }}" method="POST">
                    <div class="mb-3">
                        <label for="name" class="form-label">姓名</label>
                        <input type="text" class="form-control" id="name" name="name" value="{{ user.name or '' }}">
                    </div>
                    <div class="mb-3">
                        <label for="email" class="form-label">邮箱</label>
                        <input type="email" class="form-control" id="email" name="email" value="{{ user.email }}">
                    </div>
                    <button type="submit" class="btn btn-primary">保存修改</button>
                </form>
            </div>
        </div>
        
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">修改密码</h5>
            </div>
            <div class="card-body">
                <form action="{{ url_for('user.update_password') }}" method="POST">
                    <div class="mb-3">
                        <label for="current_password" class="form-label">当前密码</label>
                        <input type="password" class="form-control" id="current_password" name="current_password" required>
                    </div>
                    <div class="mb-3">
                        <label for="new_password" class="form-label">新密码</label>
                        <input type="password" class="form-control" id="new_password" name="new_password" required>
                    </div>
                    <div class="mb-3">
                        <label for="confirm_password" class="form-label">确认新密码</label>
                        <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                    </div>
                    <button type="submit" class="btn btn-primary">修改密码</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block styles %}
<style>
.avatar-circle {
    width: 100px;
    height: 100px;
    background-color: #007bff;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 0 auto;
}

.avatar-initials {
    font-size: 40px;
    color: white;
    font-weight: bold;
}
</style>
{% endblock %} 