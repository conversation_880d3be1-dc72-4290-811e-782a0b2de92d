# 智慧成本核算教学系统

一个集成智能对话与数据分析统计的Web系统，支持基于角色权限（教师/学生）控制功能访问。系统面向教育场景，提供教学对话支持与分析能力，为师生提供个性化、数据驱动的交互体验。

## 功能特点

### 1. 用户与权限管理
- 教师角色（管理员权限）：可访问所有功能
- 学生角色（普通用户权限）：仅可访问智能对话功能
- 用户管理：创建、编辑、删除用户
- 角色管理：分配教师/学生角色

### 2. 智能对话模块
- 类似DeepSeek的聊天对话界面
- 内置提示词系统，教师可配置提示词模板
- 多轮对话记录存储
- 接入大模型API（如GPT/Claude/OpenRouter）

### 3. 数据分析统计模块
- 自动采集学生对话数据
- 可视化图表展示（提示词使用频率、关键词词云等）
- 数据导出功能（CSV/Excel）
- 查询过滤功能

### 4. 提示词配置模块
- 提示词模板管理（添加/编辑/删除）
- 分组与标签支持
- 权限控制（公开/私有）

## 技术栈

- 后端：Python + Flask
- 前端：HTML + CSS + JavaScript + Bootstrap 5
- 数据库：SQLite（可扩展至MySQL/PostgreSQL）
- 图表：Chart.js
- 词云：jQCloud

## 安装与运行

### 环境要求
- Python 3.8+
- pip

### 安装步骤

1. 克隆仓库
```bash
git clone https://github.com/yourusername/jiaoshi_project.git
cd jiaoshi_project
```

2. 创建虚拟环境
```bash
python -m venv venv
source venv/bin/activate  # Linux/Mac
venv\Scripts\activate     # Windows
```

3. 安装依赖
```bash
pip install -r requirements.txt
```

4. 配置环境变量
```bash
# Linux/Mac
export FLASK_APP=app.py
export FLASK_ENV=development
export AI_API_KEY=your_api_key_here

# Windows
set FLASK_APP=app.py
set FLASK_ENV=development
set AI_API_KEY=your_api_key_here
```

5. 运行应用
```bash
flask run
# 或者直接运行
python app.py
```

6. 访问应用
浏览器访问 http://localhost:5000

### 默认管理员账户
- 用户名：admin
- 密码：admin123

## 项目结构

```
jiaoshi_project/
├── app/
│   ├── controllers/       # 控制器
│   ├── models/            # 数据模型
│   ├── services/          # 服务层
│   ├── static/            # 静态资源
│   │   ├── css/           # CSS样式
│   │   ├── js/            # JavaScript脚本
│   │   └── images/        # 图片资源
│   ├── templates/         # HTML模板
│   └── utils/             # 工具函数
├── app.py                 # 应用入口
├── config.py              # 配置文件
├── requirements.txt       # 依赖列表
└── README.md              # 项目说明
```

## 特定于智慧成本核算的功能

系统内置了针对成本核算课程的特定提示词和分析功能：

1. 预设提示词模板：
   - 成本要素分析
   - 边际成本计算
   - 成本差异分析

2. 分类：
   - 成本核算基础
   - 成本分析
   - 成本控制
   - 案例分析

## 许可证

MIT License
