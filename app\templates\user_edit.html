{% extends "base.html" %}

{% block title %}编辑用户 - 智慧成本核算教学系统{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">编辑用户信息</h4>
                </div>
                <div class="card-body">
                    <form action="{{ url_for('user.edit', user_id=user.id) }}" method="POST">
                        <div class="mb-3">
                            <label for="username" class="form-label">用户名</label>
                            <input type="text" class="form-control" id="username" value="{{ user.username }}" readonly disabled>
                            <small class="text-muted">用户名不可修改</small>
                        </div>
                        
                        <div class="mb-3">
                            <label for="name" class="form-label">姓名</label>
                            <input type="text" class="form-control" id="name" name="name" value="{{ user.name or '' }}">
                        </div>
                        
                        <div class="mb-3">
                            <label for="email" class="form-label">邮箱 *</label>
                            <input type="email" class="form-control" id="email" name="email" value="{{ user.email }}" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="new_password" class="form-label">新密码</label>
                            <input type="password" class="form-control" id="new_password" name="new_password">
                            <small class="text-muted">如需修改密码请填写，否则留空</small>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">用户类型 *</label>
                            {% if user.username == 'admin' %}
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle me-2"></i>管理员用户固定为教师类型，不可更改
                                </div>
                                <input type="hidden" name="user_type" value="teacher">
                            {% else %}
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="user_type" id="teacher" value="teacher" {% if user.user_type == 'teacher' %}checked{% endif %}>
                                    <label class="form-check-label" for="teacher">
                                        教师
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="user_type" id="student" value="student" {% if user.user_type == 'student' %}checked{% endif %}>
                                    <label class="form-check-label" for="student">
                                        学生
                                    </label>
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="mb-3">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="is_active" name="is_active" {% if user.is_active %}checked{% endif %}>
                                <label class="form-check-label" for="is_active">用户状态（开启表示激活）</label>
                            </div>
                        </div>
                        
                        <div class="d-flex justify-content-between">
                            <a href="{{ url_for('user.management') }}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-2"></i>返回
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>保存修改
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 