{% extends "base.html" %}

{% block title %}提示词管理 - 智慧成本核算教学系统{% endblock %}

{% block extra_css %}
<style>
    .category-header {
        background-color: #f8f9fa;
        padding: 10px 15px;
        margin-bottom: 15px;
        border-radius: 5px;
        border-left: 4px solid #007bff;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    
    .category-title {
        font-size: 1.2rem;
        font-weight: 600;
        margin-bottom: 0;
    }
    
    .prompt-table {
        margin-bottom: 30px;
    }
    
    .prompt-content {
        max-height: 100px;
        overflow-y: auto;
        background-color: #f8f9fa;
        padding: 8px;
        border-radius: 5px;
        font-family: monospace;
    }
    
    .prompt-tags {
        display: flex;
        flex-wrap: wrap;
        gap: 5px;
    }
    
    .prompt-tag {
        background-color: #e9ecef;
        padding: 2px 8px;
        border-radius: 15px;
        font-size: 0.8rem;
        display: inline-block;
    }
    
    .header-actions {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
    }
    
    .public-badge {
        background-color: #28a745;
    }
    
    .private-badge {
        background-color: #dc3545;
    }
    
    .category-filter {
        margin-bottom: 20px;
    }
    
    .category-badge {
        cursor: pointer;
        padding: 5px 10px;
        margin-right: 5px;
        border-radius: 20px;
        background-color: #e9ecef;
        transition: background-color 0.2s;
    }
    
    .category-badge.active {
        background-color: #007bff;
        color: white;
    }
    
    .add-title-btn {
        margin-left: 10px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="header-actions">
        <h2>提示词管理</h2>
        <div>
            <button type="button" class="btn btn-outline-primary" data-bs-toggle="modal" data-bs-target="#categoryModal">
                <i class="fas fa-folder-plus me-2"></i>新增分类
            </button>
            <a href="{{ url_for('prompt.create') }}" class="btn btn-primary ms-2">
                <i class="fas fa-plus me-2"></i>创建提示词
            </a>
        </div>
    </div>
    
    <!-- 分类过滤器 -->
    <div class="category-filter">
        <span class="category-badge active" data-category="all">全部</span>
        {% for category in categories %}
        <span class="category-badge" data-category="{{ category.id }}">{{ category.name }}</span>
        {% endfor %}
    </div>
    
    <!-- 提示词列表（按分类分组） -->
    <div id="promptsContainer">
        {% for category in categories %}
        <div class="category-section mb-4" data-category-id="{{ category.id }}">
            <div class="category-header">
                <h5 class="category-title">{{ category.name }}</h5>
                    <div>
                    <button class="btn btn-sm btn-outline-primary edit-category-btn" data-category-id="{{ category.id }}" data-category-name="{{ category.name }}" data-category-desc="{{ category.description }}">
                        <i class="fas fa-edit"></i> 编辑分类
                    </button>
                    <button class="btn btn-sm btn-success add-title-btn" data-category-id="{{ category.id }}">
                        <i class="fas fa-plus"></i> 添加标题
                    </button>
                </div>
            </div>
            
            <div class="table-responsive">
                <table class="table table-striped table-hover prompt-table">
                    <thead>
                        <tr>
                            <th width="5%">#</th>
                            <th width="15%">标题</th>
                            <th width="40%">内容</th>
                            <th width="15%">标签</th>
                            <th width="10%">状态</th>
                            <th width="15%">操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% set prompts_in_category = prompts|selectattr('category_id', 'equalto', category.id)|list %}
                        {% if prompts_in_category %}
                            {% for prompt in prompts_in_category %}
                            <tr>
                                <td>{{ loop.index }}</td>
                                <td>{{ prompt.title }}</td>
                                <td>
                    <div class="prompt-content">{{ prompt.content }}</div>
                                </td>
                                <td>
                                    <div class="prompt-tags">
                                        {% if prompt.tags %}
                                            {% for tag in prompt.tags.split(',') %}
                                            <span class="prompt-tag">{{ tag }}</span>
                                            {% endfor %}
                            {% endif %}
                                    </div>
                                </td>
                                <td>
                                    <span class="badge {% if prompt.is_public %}public-badge{% else %}private-badge{% endif %}">
                                        {{ "公开" if prompt.is_public else "私有" }}
                            </span>
                                </td>
                                <td>
                            <a href="{{ url_for('prompt.edit', prompt_id=prompt.id) }}" class="btn btn-sm btn-outline-primary">
                                <i class="fas fa-edit"></i>
                            </a>
                                    <button type="button" class="btn btn-sm btn-outline-danger delete-prompt-btn" 
                                            data-prompt-id="{{ prompt.id }}" 
                                            data-prompt-title="{{ prompt.title }}">
                                <i class="fas fa-trash"></i>
                            </button>
                                </td>
                            </tr>
                        {% endfor %}
                        {% else %}
                            <tr>
                                <td colspan="6" class="text-center py-3">
                                    <p class="text-muted mb-0">该分类下暂无提示词</p>
                                </td>
                            </tr>
                    {% endif %}
                    </tbody>
                </table>
            </div>
        </div>
        {% endfor %}
    
        {% if not categories %}
    <div class="text-center py-5">
            <i class="fas fa-folder-open fa-3x text-muted mb-3"></i>
            <h4>暂无分类</h4>
            <p class="text-muted">请先创建分类，然后添加提示词</p>
        </div>
        {% endif %}
    </div>
</div>

<!-- 删除确认模态框 -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">确认删除</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>确定要删除提示词 <span id="deletePromptTitle" class="fw-bold"></span> 吗？此操作不可撤销。</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <form id="deleteForm" method="POST">
                    <button type="submit" class="btn btn-danger">删除</button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- 分类管理模态框 -->
<div class="modal fade" id="categoryModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="categoryModalTitle">新增分类</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="categoryForm">
                    <input type="hidden" id="categoryId" value="">
                    <div class="mb-3">
                        <label for="categoryName" class="form-label">分类名称</label>
                        <input type="text" class="form-control" id="categoryName" required>
                    </div>
                    <div class="mb-3">
                        <label for="categoryDescription" class="form-label">分类描述</label>
                        <textarea class="form-control" id="categoryDescription" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="saveCategoryBtn">保存</button>
            </div>
        </div>
    </div>
</div>

<!-- 添加标题模态框 -->
<div class="modal fade" id="titleModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">添加标题</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="titleForm" method="POST" action="{{ url_for('prompt.create') }}">
                    <input type="hidden" id="titleCategoryId" name="category_id" value="">
                    <div class="mb-3">
                        <label for="titleName" class="form-label">标题名称</label>
                        <input type="text" class="form-control" id="titleName" name="title" required>
                    </div>
                    <div class="mb-3">
                        <label for="titleContent" class="form-label">内容</label>
                        <textarea class="form-control" id="titleContent" name="content" rows="5" required></textarea>
                    </div>
                    <div class="mb-3">
                        <label for="titleTags" class="form-label">标签（用逗号分隔）</label>
                        <input type="text" class="form-control" id="titleTags" name="tags">
                    </div>
                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" id="titleIsPublic" name="is_public" checked>
                        <label class="form-check-label" for="titleIsPublic">
                            公开（所有用户可见）
                        </label>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="saveTitleBtn">保存</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 分类过滤
        const categoryBadges = document.querySelectorAll('.category-badge');
        const categorySections = document.querySelectorAll('.category-section');
        
        categoryBadges.forEach(badge => {
            badge.addEventListener('click', function() {
                // 更新激活状态
                categoryBadges.forEach(b => b.classList.remove('active'));
                this.classList.add('active');
                
                const category = this.getAttribute('data-category');
                
                // 过滤分类
                categorySections.forEach(section => {
                    if (category === 'all' || section.getAttribute('data-category-id') === category) {
                        section.style.display = 'block';
                    } else {
                        section.style.display = 'none';
                    }
            });
        });
    });
    
    // 删除确认
        window.confirmDelete = function(promptId, title) {
        const modal = new bootstrap.Modal(document.getElementById('deleteModal'));
        document.getElementById('deletePromptTitle').textContent = title;
        document.getElementById('deleteForm').action = `/prompt/${promptId}/delete`;
        modal.show();
        };
        
        // 删除按钮点击事件
        document.querySelectorAll('.delete-prompt-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const promptId = this.getAttribute('data-prompt-id');
                const promptTitle = this.getAttribute('data-prompt-title');
                confirmDelete(promptId, promptTitle);
            });
        });
        
        // 编辑分类
        const editCategoryBtns = document.querySelectorAll('.edit-category-btn');
        const categoryModal = new bootstrap.Modal(document.getElementById('categoryModal'));
        const categoryForm = document.getElementById('categoryForm');
        const categoryId = document.getElementById('categoryId');
        const categoryName = document.getElementById('categoryName');
        const categoryDescription = document.getElementById('categoryDescription');
        const saveCategoryBtn = document.getElementById('saveCategoryBtn');
        
        editCategoryBtns.forEach(btn => {
            btn.addEventListener('click', function() {
                const id = this.getAttribute('data-category-id');
                const name = this.getAttribute('data-category-name');
                const desc = this.getAttribute('data-category-desc');
                
                document.getElementById('categoryModalTitle').textContent = '编辑分类';
                categoryId.value = id;
                categoryName.value = name;
                categoryDescription.value = desc;
                
                categoryModal.show();
            });
        });
        
        // 新增分类
        document.querySelector('[data-bs-target="#categoryModal"]').addEventListener('click', function() {
            document.getElementById('categoryModalTitle').textContent = '新增分类';
            categoryId.value = '';
            categoryName.value = '';
            categoryDescription.value = '';
        });
        
        // 保存分类
        saveCategoryBtn.addEventListener('click', function() {
            if (!categoryName.value.trim()) {
                alert('分类名称不能为空');
                return;
            }
            
            const formData = new FormData();
            formData.append('name', categoryName.value.trim());
            formData.append('description', categoryDescription.value.trim());
            
            const id = categoryId.value;
            const url = id ? `/prompt/category/${id}/edit` : '/prompt/category/create';
            
            fetch(url, {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // 刷新页面
                    window.location.reload();
                } else {
                    alert('操作失败: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('操作失败，请重试');
            });
        });
        
        // 添加标题
        const addTitleBtns = document.querySelectorAll('.add-title-btn');
        const titleModal = new bootstrap.Modal(document.getElementById('titleModal'));
        const titleForm = document.getElementById('titleForm');
        const titleCategoryId = document.getElementById('titleCategoryId');
        const saveTitleBtn = document.getElementById('saveTitleBtn');
        
        addTitleBtns.forEach(btn => {
            btn.addEventListener('click', function() {
                const categoryId = this.getAttribute('data-category-id');
                titleCategoryId.value = categoryId;
                titleModal.show();
            });
        });
        
        // 保存标题
        saveTitleBtn.addEventListener('click', function() {
            titleForm.submit();
        });
    });
</script>
{% endblock %}
