/* 全局样式 */
:root {
    --primary-color: #4361ee;
    --secondary-color: #3f37c9;
    --success-color: #4cc9f0;
    --info-color: #4895ef;
    --warning-color: #f72585;
    --danger-color: #e63946;
    --light-color: #f8f9fa;
    --dark-color: #212529;
    --transition-speed: 0.3s;
}

body {
    font-family: "Microsoft YaHei", "Segoe UI", Roboto, sans-serif;
    background-color: #f8f9fa;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    color: #333;
}

/* 顶部导航栏样式 */
.navbar {
    padding: 0.5rem 1rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
}

.navbar-brand {
    font-weight: bold;
    padding-top: 0.75rem;
    padding-bottom: 0.75rem;
    letter-spacing: 0.5px;
}

/* 头像样式 */
.avatar-circle {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    transition: transform var(--transition-speed);
}

.avatar-circle:hover {
    transform: scale(1.1);
}

/* 侧边栏样式 */
.sidebar {
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    z-index: 100;
    padding: 60px 0 0;
    box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
    transition: all var(--transition-speed);
}

.sidebar-sticky {
    position: relative;
    top: 0;
    height: calc(100vh - 60px);
    padding-top: 0.5rem;
    overflow-x: hidden;
    overflow-y: auto;
}

.sidebar .nav-link {
    font-weight: 500;
    color: #333;
    padding: 0.75rem 1.25rem;
    margin: 0.3rem 0.5rem;
    border-radius: 0.5rem;
    transition: all var(--transition-speed);
}

.sidebar .nav-link:hover {
    background-color: rgba(67, 97, 238, 0.1);
    transform: translateX(5px);
}

.sidebar .nav-link.active {
    color: var(--primary-color);
    background-color: rgba(67, 97, 238, 0.15);
    font-weight: 600;
}

.sidebar .nav-link i {
    margin-right: 0.5rem;
    transition: transform var(--transition-speed);
}

.sidebar .nav-link:hover i {
    transform: scale(1.2);
}

.sidebar-heading {
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 1px;
    color: #6c757d;
}

/* 主内容区样式 */
main {
    padding-top: 1.5rem;
    padding-bottom: 1.5rem;
    flex: 1;
}

/* 页脚样式 */
.footer {
    background-color: #fff;
    padding: 1rem 0;
    margin-top: auto;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
}

/* 卡片样式 */
.card {
    margin-bottom: 20px;
    border: none;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    border-radius: 0.75rem;
    overflow: hidden;
    transition: all var(--transition-speed);
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.card-header {
    background-color: #fff;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    font-weight: 600;
    padding: 1rem 1.25rem;
}

/* 按钮样式 */
.btn {
    border-radius: 0.5rem;
    padding: 0.5rem 1.25rem;
    font-weight: 500;
    letter-spacing: 0.5px;
    transition: all var(--transition-speed);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border: none;
}

.btn-success {
    background: linear-gradient(135deg, #2ecc71, #27ae60);
    border: none;
}

/* 表格样式 */
.table {
    background-color: #fff;
    border-radius: 0.5rem;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.table thead th {
    background-color: rgba(67, 97, 238, 0.05);
    border-bottom: none;
    font-weight: 600;
}

/* 聊天界面样式 */
.chat-container {
    height: 70vh;
    display: flex;
    flex-direction: column;
    border-radius: 0.75rem;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    background-color: #fff;
    overflow: hidden;
}

.chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: 20px;
    background-color: #f8f9fa;
    margin-bottom: 0;
}

.message {
    margin-bottom: 20px;
    padding: 12px 18px;
    border-radius: 18px;
    max-width: 75%;
    position: relative;
    animation: fadeIn 0.5s ease;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.message-user {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    align-self: flex-end;
    margin-left: auto;
    border-bottom-right-radius: 5px;
}

.message-assistant {
    background-color: #e9ecef;
    color: #212529;
    align-self: flex-start;
    border-bottom-left-radius: 5px;
}

/* Markdown 内容样式 */
.message-content h1,
.message-content h2,
.message-content h3,
.message-content h4,
.message-content h5,
.message-content h6 {
    margin-top: 1rem;
    margin-bottom: 0.5rem;
    font-weight: 600;
}

.message-content h1 { font-size: 1.5rem; }
.message-content h2 { font-size: 1.3rem; }
.message-content h3 { font-size: 1.1rem; }
.message-content h4 { font-size: 1rem; }
.message-content h5 { font-size: 0.9rem; }
.message-content h6 { font-size: 0.8rem; }

.message-content p {
    margin-bottom: 0.75rem;
    line-height: 1.6;
}

.message-content strong {
    font-weight: 600;
}

.message-content em {
    font-style: italic;
}

.message-content code {
    background-color: rgba(0, 0, 0, 0.1);
    padding: 0.2rem 0.4rem;
    border-radius: 0.25rem;
    font-family: 'Courier New', monospace;
    font-size: 0.9em;
}

.message-content pre {
    background-color: rgba(0, 0, 0, 0.05);
    padding: 1rem;
    border-radius: 0.5rem;
    overflow-x: auto;
    margin: 0.75rem 0;
}

.message-content pre code {
    background-color: transparent;
    padding: 0;
}

.message-content ul,
.message-content ol {
    margin-bottom: 0.75rem;
    padding-left: 1.5rem;
}

.message-content li {
    margin-bottom: 0.25rem;
    line-height: 1.5;
}

.message-content blockquote {
    border-left: 4px solid var(--primary-color);
    padding-left: 1rem;
    margin: 0.75rem 0;
    font-style: italic;
    color: #6c757d;
}

.message-content table {
    width: 100%;
    border-collapse: collapse;
    margin: 0.75rem 0;
}

.message-content th,
.message-content td {
    border: 1px solid #dee2e6;
    padding: 0.5rem;
    text-align: left;
}

.message-content th {
    background-color: rgba(0, 0, 0, 0.05);
    font-weight: 600;
}

.message-content hr {
    margin: 1rem 0;
    border: none;
    border-top: 1px solid #dee2e6;
}

.chat-input {
    border-top: 1px solid rgba(0, 0, 0, 0.05);
    padding: 15px;
    background-color: #fff;
}

/* 表单元素样式 */
.form-control {
    border-radius: 0.5rem;
    padding: 0.75rem 1rem;
    border: 1px solid rgba(0, 0, 0, 0.1);
    transition: all var(--transition-speed);
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.25rem rgba(67, 97, 238, 0.25);
}

/* 提示词卡片样式 */
.prompt-card {
    cursor: pointer;
    transition: all var(--transition-speed);
    height: 100%;
}

.prompt-card:hover {
    transform: translateY(-7px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.prompt-card .card-body {
    display: flex;
    flex-direction: column;
    height: 100%;
}

.prompt-card .card-title {
    font-weight: 600;
    margin-bottom: 0.75rem;
}

.prompt-card .card-text {
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
}

/* 分析页面样式 */
.stat-card {
    text-align: center;
    padding: 25px;
    transition: all var(--transition-speed);
    border-radius: 0.75rem;
}

.stat-card:hover {
    transform: translateY(-5px);
}

.stat-card .number {
    font-size: 2.5rem;
    font-weight: bold;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    margin-bottom: 0.5rem;
}

.stat-card .label {
    color: #6c757d;
    font-size: 1rem;
    font-weight: 500;
}

/* 图表容器 */
.chart-container {
    background-color: #fff;
    border-radius: 0.75rem;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    padding: 20px;
    margin-bottom: 30px;
    transition: all var(--transition-speed);
}

.chart-container:hover {
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

/* 加载动画 */
.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(0, 0, 0, 0.1);
    border-left-color: var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* 徽章样式 */
.badge {
    padding: 0.5em 0.75em;
    border-radius: 0.5rem;
    font-weight: 500;
}

/* 响应式调整 */
@media (max-width: 767.98px) {
    .sidebar {
        position: static;
        height: auto;
        padding-top: 0;
    }
    
    main {
        margin-left: 0 !important;
    }
    
    .chat-container {
        height: 60vh;
    }
}
