{% extends "base.html" %}

{% block title %}创建用户 - 智慧成本核算教学系统{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">创建新用户</h4>
                </div>
                <div class="card-body">
                    <form action="{{ url_for('user.create') }}" method="POST">
                        <div class="mb-3">
                            <label for="username" class="form-label">用户名 *</label>
                            <input type="text" class="form-control" id="username" name="username" required>
                            <small class="text-muted">用户登录时使用的唯一标识，创建后不可修改</small>
                            <small class="text-muted d-block mt-1">注意：用户名为"admin"的用户将自动设为教师类型</small>
                        </div>
                        
                        <div class="mb-3">
                            <label for="name" class="form-label">姓名</label>
                            <input type="text" class="form-control" id="name" name="name">
                        </div>
                        
                        <div class="mb-3">
                            <label for="email" class="form-label">邮箱 *</label>
                            <input type="email" class="form-control" id="email" name="email" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="password" class="form-label">密码 *</label>
                            <input type="password" class="form-control" id="password" name="password" required>
                            <small class="text-muted">密码长度不少于6个字符</small>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">用户类型 *</label>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="user_type" id="teacher" value="teacher">
                                <label class="form-check-label" for="teacher">
                                    教师
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="user_type" id="student" value="student" checked>
                                <label class="form-check-label" for="student">
                                    学生
                                </label>
                            </div>
                        </div>
                        
                        <div class="d-flex justify-content-between">
                            <a href="{{ url_for('user.management') }}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-2"></i>返回
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-user-plus me-2"></i>创建用户
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 