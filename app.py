import os
from flask import Flask, render_template, redirect, url_for
from flask_login import current_user

from app.models import init_app as init_models
from app.controllers import init_app as init_controllers
from app.utils.db_utils import init_db
from config import config

def create_app(config_name='default'):
    """创建Flask应用"""
    app = Flask(__name__, 
               template_folder='app/templates',
               static_folder='app/static')
    
    # 加载配置
    app.config.from_object(config[config_name])
    
    # 初始化数据库和模型
    init_models(app)
    
    # 初始化控制器
    init_controllers(app)
    
    # 注册错误处理器
    register_error_handlers(app)
    
    # 注册首页路由
    @app.route('/')
    def index():
        if current_user.is_authenticated:
            return redirect(url_for('chat.index'))
        return redirect(url_for('auth.login'))
    
    return app

def register_error_handlers(app):
    """注册错误处理器"""
    @app.errorhandler(404)
    def page_not_found(e):
        return render_template('error.html', error_code=404, error_message='页面不存在'), 404
    
    @app.errorhandler(403)
    def forbidden(e):
        return render_template('error.html', error_code=403, error_message='禁止访问'), 403
    
    @app.errorhandler(500)
    def internal_server_error(e):
        return render_template('error.html', error_code=500, error_message='服务器内部错误'), 500

if __name__ == '__main__':
    app = create_app(os.getenv('FLASK_CONFIG') or 'development')
    
    # 初始化数据库和示例数据
    with app.app_context():
        init_db(app)
    
    app.run(host='0.0.0.0', port=5000, debug=True)
