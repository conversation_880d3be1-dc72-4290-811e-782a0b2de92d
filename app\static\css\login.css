:root {
    --primary-color: #4361ee;
    --secondary-color: #3f37c9;
    --success-color: #4cc9f0;
    --info-color: #4895ef;
    --warning-color: #f72585;
    --danger-color: #e63946;
    --light-color: #f8f9fa;
    --dark-color: #212529;
    --transition-speed: 0.3s;
}

body {
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
    margin: 0;
    font-family: "Microsoft YaHei", "Segoe UI", Roboto, sans-serif;
}

.login-container {
    width: 100%;
    max-width: 1200px;
    margin: 2rem auto;
    box-shadow: 0 15px 50px rgba(0, 0, 0, 0.1);
    border-radius: 20px;
    overflow: hidden;
    background-color: #fff;
}

.login-container .row {
    margin: 0;
}

/* 左侧图片区域 */
.login-image-container {
    padding: 0;
    position: relative;
    min-height: 600px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
}

.login-image {
    position: relative;
    height: 100%;
    width: 100%;
    overflow: hidden;
}

.login-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
    opacity: 0.5;
}

.login-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    background: linear-gradient(135deg, rgba(67, 97, 238, 0.8), rgba(63, 55, 201, 0.9));
    color: #fff;
    padding: 2rem;
    text-align: center;
}

.login-overlay h2 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.login-overlay p {
    font-size: 1.2rem;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
    margin-bottom: 1.5rem;
}

/* 特性列表 */
.features {
    display: flex;
    flex-direction: column;
    gap: 15px;
    margin-top: 2rem;
}

.feature-item {
    font-size: 1.1rem;
    display: flex;
    align-items: center;
    padding: 10px 20px;
    background-color: rgba(255, 255, 255, 0.2);
    border-radius: 50px;
    backdrop-filter: blur(5px);
    transition: all var(--transition-speed);
}

.feature-item:hover {
    transform: translateX(10px);
    background-color: rgba(255, 255, 255, 0.3);
}

.feature-item i {
    font-size: 1.2rem;
}

/* 右侧登录表单 */
.login-form-container {
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #fff;
}

.login-form {
    width: 100%;
    max-width: 400px;
    padding: 3rem;
}

.app-logo {
    width: 80px;
    height: 80px;
    margin: 0 auto 1.5rem;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 10px 20px rgba(67, 97, 238, 0.3);
}

.app-logo i {
    font-size: 2.5rem;
    color: white;
}

.login-form h3 {
    font-weight: 700;
    color: #333;
    margin-bottom: 0.5rem;
}

.form-control {
    border-radius: 0.5rem;
    padding: 0.75rem 1rem;
    height: auto;
    border: 1px solid rgba(0, 0, 0, 0.1);
    transition: all var(--transition-speed);
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.25rem rgba(67, 97, 238, 0.25);
}

.input-group-text {
    background-color: #f8f9fa;
    border: 1px solid rgba(0, 0, 0, 0.1);
    color: #6c757d;
}

.btn-primary {
    padding: 0.75rem 1rem;
    font-weight: 500;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border: none;
    box-shadow: 0 5px 15px rgba(67, 97, 238, 0.3);
    transition: all var(--transition-speed);
}

.btn-primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(67, 97, 238, 0.4);
}

.form-check-input:checked {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

/* 登录页面不显示页眉和页脚 */
header, footer {
    display: none;
}

/* 响应式设计 */
@media (max-width: 767.98px) {
    .login-container {
        box-shadow: none;
        max-width: 100%;
        margin: 0;
        border-radius: 0;
    }
    
    .login-image-container {
        min-height: 300px;
    }
    
    .login-form {
        padding: 2rem 1.5rem;
    }
}
