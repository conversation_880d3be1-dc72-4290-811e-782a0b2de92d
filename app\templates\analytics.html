{% extends "base.html" %}

{% block title %}数据分析 - 智慧成本核算教学系统{% endblock %}

{% block content %}
<div class="row mb-4 animate__animated animate__fadeIn">
    <div class="col-md-8">
        <h2><i class="fas fa-chart-line me-2"></i>数据分析</h2>
        <p class="text-muted">查看学生学习数据和使用情况统计</p>
    </div>
    <div class="col-md-4 text-end">
        <a href="{{ url_for('analytics.export_excel') }}" class="btn btn-success">
            <i class="fas fa-download me-2"></i> 导出数据
        </a>
    </div>
</div>

<div class="row mb-4">
    <!-- 基础统计卡片 -->
    <div class="col-md-3 animate__animated animate__fadeInUp" style="animation-delay: 0.1s">
        <div class="card mb-4">
            <div class="card-body stat-card">
                <div class="stat-icon mb-3">
                    <i class="fas fa-user-graduate"></i>
                </div>
                <div class="number" id="total-students">--</div>
                <div class="label">学生总数</div>
            </div>
        </div>
    </div>
    <div class="col-md-3 animate__animated animate__fadeInUp" style="animation-delay: 0.2s">
        <div class="card mb-4">
            <div class="card-body stat-card">
                <div class="stat-icon mb-3">
                    <i class="fas fa-comments"></i>
                </div>
                <div class="number" id="total-sessions">--</div>
                <div class="label">会话总数</div>
            </div>
        </div>
    </div>
    <div class="col-md-3 animate__animated animate__fadeInUp" style="animation-delay: 0.3s">
        <div class="card mb-4">
            <div class="card-body stat-card">
                <div class="stat-icon mb-3">
                    <i class="fas fa-comment-dots"></i>
                </div>
                <div class="number" id="total-messages">--</div>
                <div class="label">消息总数</div>
            </div>
        </div>
    </div>
    <div class="col-md-3 animate__animated animate__fadeInUp" style="animation-delay: 0.4s">
        <div class="card mb-4">
            <div class="card-body stat-card">
                <div class="stat-icon mb-3">
                    <i class="fas fa-check-circle"></i>
                </div>
                <div class="number" id="overall-resolution-rate">--</div>
                <div class="label">问题解决率</div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- 活跃用户统计 -->
    <div class="col-md-6 animate__animated animate__fadeInUp" style="animation-delay: 0.5s">
        <div class="card mb-4 chart-container">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="fas fa-users me-2"></i>活跃用户</h5>
                <div class="card-actions">
                    <button class="btn btn-sm btn-outline-secondary" id="refresh-users-btn">
                        <i class="fas fa-sync-alt"></i>
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>用户名</th>
                                <th>姓名</th>
                                <th>消息数</th>
                                <th>活跃度</th>
                            </tr>
                        </thead>
                        <tbody id="active-users-table">
                            <tr>
                                <td colspan="4" class="text-center">
                                    <div class="loading-spinner mx-auto"></div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 每日消息统计 -->
    <div class="col-md-6 animate__animated animate__fadeInUp" style="animation-delay: 0.6s">
        <div class="card mb-4 chart-container">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="fas fa-chart-area me-2"></i>每日消息统计</h5>
                <div class="btn-group">
                    <button class="btn btn-sm btn-outline-secondary active" data-period="week">周</button>
                    <button class="btn btn-sm btn-outline-secondary" data-period="month">月</button>
                </div>
            </div>
            <div class="card-body">
                <div id="daily-messages-chart" style="height: 300px;"></div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- 问题分类统计 -->
    <div class="col-md-6 animate__animated animate__fadeInUp" style="animation-delay: 0.7s">
        <div class="card mb-4 chart-container">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="fas fa-pie-chart me-2"></i>问题分类统计</h5>
                <div class="dropdown">
                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" id="chartTypeDropdown" data-bs-toggle="dropdown">
                        图表类型
                    </button>
                    <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="chartTypeDropdown">
                        <li><a class="dropdown-item" href="#" data-chart-type="pie">饼图</a></li>
                        <li><a class="dropdown-item" href="#" data-chart-type="bar">柱状图</a></li>
                    </ul>
                </div>
            </div>
            <div class="card-body">
                <div id="category-chart" style="height: 300px;"></div>
            </div>
        </div>
    </div>
    
    <!-- 问题解决率统计 -->
    <div class="col-md-6 animate__animated animate__fadeInUp" style="animation-delay: 0.8s">
        <div class="card mb-4 chart-container">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="fas fa-tasks me-2"></i>问题解决率统计</h5>
                <div class="form-check form-switch">
                    <input class="form-check-input" type="checkbox" id="showChartSwitch">
                    <label class="form-check-label" for="showChartSwitch">图表视图</label>
                </div>
            </div>
            <div class="card-body">
                <div id="resolution-table-view">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>问题分类</th>
                                    <th>已解决</th>
                                    <th>未解决</th>
                                    <th>总数</th>
                                    <th>解决率</th>
                                </tr>
                            </thead>
                            <tbody id="resolution-stats-table">
                                <tr>
                                    <td colspan="5" class="text-center">
                                        <div class="loading-spinner mx-auto"></div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                <div id="resolution-chart-view" style="height: 300px; display: none;"></div>
            </div>
        </div>
    </div>
</div>

<!-- 新增提示词统计和学生问题列表 -->
<div class="row">
    <!-- 提示词使用统计 -->
    <div class="col-md-6 animate__animated animate__fadeInUp" style="animation-delay: 0.9s">
        <div class="card mb-4 chart-container">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="fas fa-lightbulb me-2"></i>提示词使用统计</h5>
                <div class="dropdown">
                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" id="promptChartTypeDropdown" data-bs-toggle="dropdown">
                        图表类型
                    </button>
                    <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="promptChartTypeDropdown">
                        <li><a class="dropdown-item" href="#" data-prompt-chart-type="bar">柱状图</a></li>
                        <li><a class="dropdown-item" href="#" data-prompt-chart-type="pie">饼图</a></li>
                    </ul>
                </div>
            </div>
            <div class="card-body">
                <div id="prompt-chart" style="height: 300px;"></div>
            </div>
        </div>
    </div>
    
    <!-- 学生常见问题列表 -->
    <div class="col-md-6 animate__animated animate__fadeInUp" style="animation-delay: 1.0s">
        <div class="card mb-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="fas fa-question-circle me-2"></i>学生常见问题</h5>
                <button class="btn btn-sm btn-outline-secondary" id="refresh-questions-btn">
                    <i class="fas fa-sync-alt"></i>
                </button>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>问题内容</th>
                                <th>分类</th>
                                <th>频率</th>
                            </tr>
                        </thead>
                        <tbody id="top-questions-table">
                            <tr>
                                <td colspan="3" class="text-center">
                                    <div class="loading-spinner mx-auto"></div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 图表实例
        let dailyMessagesChart = null;
        let categoryChart = null;
        let resolutionChart = null;
        let promptChart = null;
        
        // 颜色配置
        const colors = [
            '#4361ee', '#3a0ca3', '#7209b7', '#f72585', 
            '#4cc9f0', '#4895ef', '#560bad', '#b5179e',
            '#3f37c9', '#4361ee', '#4cc9f0', '#480ca8'
        ];
        
        // 加载分析数据
        function loadAnalyticsData() {
            fetch('{{ url_for("analytics.get_data") }}')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // 更新基础统计
                        animateNumber('total-students', data.data.summary.total_users);
                        animateNumber('total-sessions', data.data.summary.total_sessions);
                        animateNumber('total-messages', data.data.summary.total_messages);
                        animateNumber('overall-resolution-rate', data.data.summary.overall_resolution_rate, '%');
                        
                        // 更新活跃用户表格
                        updateActiveUsersTable(data.data.active_users);
                        
                        // 创建每日消息图表
                        createDailyMessagesChart(data.data.daily_messages);
                        
                        // 创建问题分类图表
                        createCategoryChart(data.data.category_stats);
                        
                        // 更新问题解决率表格和图表
                        updateResolutionStats(data.data.resolution_stats);
                        
                        // 创建提示词使用统计图表
                        createPromptChart(data.data.prompt_stats);
                        
                        // 更新学生常见问题列表
                        updateTopQuestionsTable(data.data.top_questions);
                    }
                })
                .catch(error => {
                    console.error('Error loading analytics data:', error);
                });
        }
        
        // 数字动画效果
        function animateNumber(elementId, target, suffix = '') {
            const element = document.getElementById(elementId);
            const start = 0;
            const duration = 1500;
            const startTime = performance.now();
            
            function updateNumber(currentTime) {
                const elapsedTime = currentTime - startTime;
                if (elapsedTime < duration) {
                    const progress = elapsedTime / duration;
                    const current = Math.floor(progress * target);
                    element.textContent = current + suffix;
                    requestAnimationFrame(updateNumber);
                } else {
                    element.textContent = target + suffix;
                }
            }
            
            requestAnimationFrame(updateNumber);
        }
        
        // 更新活跃用户表格
        function updateActiveUsersTable(activeUsers) {
            const activeUsersTable = document.getElementById('active-users-table');
            activeUsersTable.innerHTML = '';
            
            if (activeUsers.length > 0) {
                activeUsers.forEach(user => {
                    // 计算活跃度进度条
                    const maxCount = Math.max(...activeUsers.map(u => u.message_count));
                    const percentage = Math.round((user.message_count / maxCount) * 100);
                    
                    activeUsersTable.innerHTML += `
                        <tr>
                            <td>${user.username}</td>
                            <td>${user.name}</td>
                            <td>${user.message_count}</td>
                            <td>
                                <div class="progress" style="height: 6px;">
                                    <div class="progress-bar" role="progressbar" style="width: ${percentage}%;" 
                                         aria-valuenow="${percentage}" aria-valuemin="0" aria-valuemax="100"></div>
                                </div>
                            </td>
                        </tr>
                    `;
                });
            } else {
                activeUsersTable.innerHTML = `
                    <tr>
                        <td colspan="4" class="text-center">暂无数据</td>
                    </tr>
                `;
            }
        }
        
        // 创建每日消息图表
        function createDailyMessagesChart(dailyMessages) {
            const chartDom = document.getElementById('daily-messages-chart');
            
            // 检查是否有有效数据
            if (!dailyMessages || !dailyMessages.labels || dailyMessages.labels.length === 0) {
                chartDom.innerHTML = '<div class="text-center py-5 text-muted"><i class="fas fa-info-circle fa-3x mb-3"></i><p>暂无消息数据</p></div>';
                return;
            }
            
            // 如果图表已存在，销毁它
            if (dailyMessagesChart) {
                dailyMessagesChart.dispose();
            }
            
            dailyMessagesChart = echarts.init(chartDom);
            
            const option = {
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'shadow'
                    }
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    containLabel: true
                },
                xAxis: {
                    type: 'category',
                    data: dailyMessages.labels,
                    axisLabel: {
                        rotate: 45
                    }
                },
                yAxis: {
                    type: 'value',
                    minInterval: 1
                },
                series: [
                    {
                        name: '消息数',
                        type: 'line',
                        data: dailyMessages.data,
                        smooth: true,
                        lineStyle: {
                            width: 3,
                            shadowColor: 'rgba(0,0,0,0.2)',
                            shadowBlur: 10,
                            shadowOffsetY: 10
                        },
                        itemStyle: {
                            color: '#4361ee'
                        },
                        areaStyle: {
                            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                                { offset: 0, color: 'rgba(67, 97, 238, 0.6)' },
                                { offset: 1, color: 'rgba(67, 97, 238, 0.1)' }
                            ])
                        }
                    }
                ],
                animation: true
            };
            
            dailyMessagesChart.setOption(option);
            
            // 响应窗口大小变化
            window.addEventListener('resize', function() {
                dailyMessagesChart.resize();
            });
        }
        
        // 创建问题分类图表
        function createCategoryChart(categoryStats) {
            const chartDom = document.getElementById('category-chart');
            
            // 检查是否有有效数据
            if (!categoryStats || !categoryStats.labels || categoryStats.labels.length === 0 || 
                (categoryStats.labels.length === 1 && categoryStats.labels[0] === '暂无数据')) {
                chartDom.innerHTML = '<div class="text-center py-5 text-muted"><i class="fas fa-info-circle fa-3x mb-3"></i><p>暂无分类数据</p></div>';
                return;
            }
            
            // 如果图表已存在，销毁它
            if (categoryChart) {
                categoryChart.dispose();
            }
            
            categoryChart = echarts.init(chartDom);
            
            let option = {
                tooltip: {
                    trigger: 'item',
                    formatter: '{b}: {c} ({d}%)'
                },
                legend: {
                    orient: 'vertical',
                    right: 10,
                    top: 'center',
                    formatter: '{name}'
                },
                series: [
                    {
                        name: '问题分类',
                        type: 'pie',
                        radius: ['40%', '70%'],
                        avoidLabelOverlap: false,
                        itemStyle: {
                            borderRadius: 10,
                            borderColor: '#fff',
                            borderWidth: 2
                        },
                        label: {
                            show: false,
                            position: 'center'
                        },
                        emphasis: {
                            label: {
                                show: true,
                                fontSize: '18',
                                fontWeight: 'bold'
                            }
                        },
                        labelLine: {
                            show: false
                        },
                        data: categoryStats.labels.map((label, index) => {
                            return {
                                value: categoryStats.data[index],
                                name: label,
                                itemStyle: {
                                    color: colors[index % colors.length]
                                }
                            };
                        })
                    }
                ],
                animation: true
            };
            
            categoryChart.setOption(option);
            
            // 响应窗口大小变化
            window.addEventListener('resize', function() {
                categoryChart.resize();
            });
            
            // 切换图表类型
            document.querySelectorAll('[data-chart-type]').forEach(button => {
                button.addEventListener('click', function(e) {
                    e.preventDefault();
                    const chartType = this.getAttribute('data-chart-type');
                    
                    if (chartType === 'pie') {
                        // 饼图配置
                        option = {
                            tooltip: {
                                trigger: 'item',
                                formatter: '{b}: {c} ({d}%)'
                            },
                            legend: {
                                orient: 'vertical',
                                right: 10,
                                top: 'center',
                                formatter: '{name}'
                            },
                            series: [
                                {
                                    name: '问题分类',
                                    type: 'pie',
                                    radius: ['40%', '70%'],
                                    avoidLabelOverlap: false,
                                    itemStyle: {
                                        borderRadius: 10,
                                        borderColor: '#fff',
                                        borderWidth: 2
                                    },
                                    label: {
                                        show: false,
                                        position: 'center'
                                    },
                                    emphasis: {
                                        label: {
                                            show: true,
                                            fontSize: '18',
                                            fontWeight: 'bold'
                                        }
                                    },
                                    labelLine: {
                                        show: false
                                    },
                                    data: categoryStats.labels.map((label, index) => {
                                        return {
                                            value: categoryStats.data[index],
                                            name: label,
                                            itemStyle: {
                                                color: colors[index % colors.length]
                                            }
                                        };
                                    })
                                }
                            ],
                            animation: true
                        };
                    } else if (chartType === 'bar') {
                        // 柱状图配置
                        option = {
                            tooltip: {
                                trigger: 'axis',
                                axisPointer: {
                                    type: 'shadow'
                                }
                            },
                            legend: {
                                show: false
                            },
                            grid: {
                                left: '3%',
                                right: '4%',
                                bottom: '3%',
                                containLabel: true
                            },
                            xAxis: {
                                type: 'category',
                                data: categoryStats.labels,
                                axisLabel: {
                                    rotate: 45
                                }
                            },
                            yAxis: {
                                type: 'value',
                                minInterval: 1
                            },
                            series: [
                                {
                                    name: '问题数量',
                                    type: 'bar',
                                    data: categoryStats.data.map((value, index) => {
                                        return {
                                            value: value,
                                            itemStyle: {
                                                color: colors[index % colors.length]
                                            }
                                        };
                                    }),
                                    barWidth: '60%',
                                    label: {
                                        show: true,
                                        position: 'top'
                                    }
                                }
                            ],
                            animation: true
                        };
                    }
                    
                    // 重新设置图表选项
                    categoryChart.setOption(option, true);
                });
            });
        }
        
        // 更新问题解决率统计
        function updateResolutionStats(resolutionStats) {
            const resolutionStatsTable = document.getElementById('resolution-stats-table');
            resolutionStatsTable.innerHTML = '';
            
            if (resolutionStats.length > 0) {
                const chartData = {
                    categories: [],
                    resolved: [],
                    unresolved: []
                };
                
                resolutionStats.forEach(stat => {
                    // 更新表格
                    resolutionStatsTable.innerHTML += `
                        <tr>
                            <td>${stat.category}</td>
                            <td>${stat.resolved}</td>
                            <td>${stat.unresolved}</td>
                            <td>${stat.total}</td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="progress flex-grow-1 me-2" style="height: 6px;">
                                        <div class="progress-bar bg-success" role="progressbar" style="width: ${stat.resolution_rate}%;" 
                                             aria-valuenow="${stat.resolution_rate}" aria-valuemin="0" aria-valuemax="100"></div>
                                    </div>
                                    <span>${stat.resolution_rate}%</span>
                                </div>
                            </td>
                        </tr>
                    `;
                    
                    // 收集图表数据
                    chartData.categories.push(stat.category);
                    chartData.resolved.push(stat.resolved);
                    chartData.unresolved.push(stat.unresolved);
                });
                
                // 创建解决率图表
                createResolutionChart(chartData);
            } else {
                resolutionStatsTable.innerHTML = `
                    <tr>
                        <td colspan="5" class="text-center">暂无数据</td>
                    </tr>
                `;
                
                document.getElementById('resolution-chart-view').innerHTML = '<div class="text-center py-5 text-muted"><i class="fas fa-info-circle fa-3x mb-3"></i><p>暂无解决率数据</p></div>';
            }
            
            // 切换视图
            const showChartSwitch = document.getElementById('showChartSwitch');
            const tableView = document.getElementById('resolution-table-view');
            const chartView = document.getElementById('resolution-chart-view');
            
            showChartSwitch.addEventListener('change', function() {
                if (this.checked) {
                    tableView.style.display = 'none';
                    chartView.style.display = 'block';
                    if (resolutionChart) {
                        resolutionChart.resize();
                    }
                } else {
                    tableView.style.display = 'block';
                    chartView.style.display = 'none';
                }
            });
        }
        
        // 创建解决率图表
        function createResolutionChart(data) {
            const chartDom = document.getElementById('resolution-chart-view');
            
            // 检查是否有有效数据
            if (!data || !data.categories || data.categories.length === 0 || 
                (data.categories.length === 1 && data.categories[0] === '暂无数据')) {
                chartDom.innerHTML = '<div class="text-center py-5 text-muted"><i class="fas fa-info-circle fa-3x mb-3"></i><p>暂无解决率数据</p></div>';
                return;
            }
            
            // 如果图表已存在，销毁它
            if (resolutionChart) {
                resolutionChart.dispose();
            }
            
            resolutionChart = echarts.init(chartDom);
            
            const option = {
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'shadow'
                    }
                },
                legend: {
                    data: ['已解决', '未解决']
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    containLabel: true
                },
                xAxis: {
                    type: 'value'
                },
                yAxis: {
                    type: 'category',
                    data: data.categories
                },
                series: [
                    {
                        name: '已解决',
                        type: 'bar',
                        stack: 'total',
                        label: {
                            show: true,
                            position: 'inside'
                        },
                        emphasis: {
                            focus: 'series'
                        },
                        data: data.resolved,
                        itemStyle: {
                            color: '#4cc9f0'
                        }
                    },
                    {
                        name: '未解决',
                        type: 'bar',
                        stack: 'total',
                        label: {
                            show: true,
                            position: 'inside'
                        },
                        emphasis: {
                            focus: 'series'
                        },
                        data: data.unresolved,
                        itemStyle: {
                            color: '#f72585'
                        }
                    }
                ]
            };
            
            resolutionChart.setOption(option);
            
            // 响应窗口大小变化
            window.addEventListener('resize', function() {
                if (document.getElementById('resolution-chart-view').style.display !== 'none') {
                    resolutionChart.resize();
                }
            });
        }
        
        // 创建提示词使用统计图表
        function createPromptChart(promptStats) {
            const chartDom = document.getElementById('prompt-chart');
            
            // 检查是否有有效数据
            if (!promptStats || !promptStats.chart_data || !promptStats.chart_data.labels || 
                promptStats.chart_data.labels.length === 0 || 
                (promptStats.chart_data.labels.length === 1 && promptStats.chart_data.labels[0] === '暂无数据')) {
                chartDom.innerHTML = '<div class="text-center py-5 text-muted"><i class="fas fa-info-circle fa-3x mb-3"></i><p>暂无提示词使用数据</p></div>';
                return;
            }
            
            // 如果图表已存在，销毁它
            if (promptChart) {
                promptChart.dispose();
            }
            
            promptChart = echarts.init(chartDom);
            
            // 默认使用柱状图
            let option = {
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'shadow'
                    }
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '15%',
                    containLabel: true
                },
                xAxis: {
                    type: 'category',
                    data: promptStats.chart_data.labels,
                    axisLabel: {
                        rotate: 45,
                        interval: 0
                    }
                },
                yAxis: {
                    type: 'value',
                    minInterval: 1
                },
                series: [
                    {
                        name: '使用次数',
                        type: 'bar',
                        data: promptStats.chart_data.data.map((value, index) => {
                            return {
                                value: value,
                                itemStyle: {
                                    color: colors[index % colors.length]
                                }
                            };
                        }),
                        barWidth: '60%',
                        label: {
                            show: true,
                            position: 'top'
                        }
                    }
                ],
                animation: true
            };
            
            promptChart.setOption(option);
            
            // 响应窗口大小变化
            window.addEventListener('resize', function() {
                promptChart.resize();
            });
            
            // 切换图表类型
            document.querySelectorAll('[data-prompt-chart-type]').forEach(button => {
                button.addEventListener('click', function(e) {
                    e.preventDefault();
                    const chartType = this.getAttribute('data-prompt-chart-type');
                    
                    if (chartType === 'bar') {
                        // 柱状图配置
                        option = {
                            tooltip: {
                                trigger: 'axis',
                                axisPointer: {
                                    type: 'shadow'
                                }
                            },
                            grid: {
                                left: '3%',
                                right: '4%',
                                bottom: '15%',
                                containLabel: true
                            },
                            xAxis: {
                                type: 'category',
                                data: promptStats.chart_data.labels,
                                axisLabel: {
                                    rotate: 45,
                                    interval: 0
                                }
                            },
                            yAxis: {
                                type: 'value',
                                minInterval: 1
                            },
                            series: [
                                {
                                    name: '使用次数',
                                    type: 'bar',
                                    data: promptStats.chart_data.data.map((value, index) => {
                                        return {
                                            value: value,
                                            itemStyle: {
                                                color: colors[index % colors.length]
                                            }
                                        };
                                    }),
                                    barWidth: '60%',
                                    label: {
                                        show: true,
                                        position: 'top'
                                    }
                                }
                            ],
                            animation: true
                        };
                    } else if (chartType === 'pie') {
                        // 饼图配置
                        option = {
                            tooltip: {
                                trigger: 'item',
                                formatter: '{b}: {c} ({d}%)'
                            },
                            legend: {
                                orient: 'horizontal',
                                bottom: 10,
                                data: promptStats.chart_data.labels
                            },
                            series: [
                                {
                                    name: '提示词使用',
                                    type: 'pie',
                                    radius: ['40%', '70%'],
                                    avoidLabelOverlap: false,
                                    itemStyle: {
                                        borderRadius: 10,
                                        borderColor: '#fff',
                                        borderWidth: 2
                                    },
                                    label: {
                                        show: false,
                                        position: 'center'
                                    },
                                    emphasis: {
                                        label: {
                                            show: true,
                                            fontSize: '18',
                                            fontWeight: 'bold'
                                        }
                                    },
                                    labelLine: {
                                        show: false
                                    },
                                    data: promptStats.chart_data.labels.map((label, index) => {
                                        return {
                                            value: promptStats.chart_data.data[index],
                                            name: label,
                                            itemStyle: {
                                                color: colors[index % colors.length]
                                            }
                                        };
                                    })
                                }
                            ],
                            animation: true
                        };
                    }
                    
                    // 重新设置图表选项
                    promptChart.setOption(option, true);
                });
            });
        }
        
        // 更新学生常见问题表格
        function updateTopQuestionsTable(questions) {
            const questionsTable = document.getElementById('top-questions-table');
            questionsTable.innerHTML = '';
            
            if (questions && questions.length > 0) {
                questions.forEach(question => {
                    questionsTable.innerHTML += `
                        <tr>
                            <td>${question.question}</td>
                            <td><span class="badge bg-info">${question.category}</span></td>
                            <td>${question.frequency}</td>
                        </tr>
                    `;
                });
            } else {
                questionsTable.innerHTML = `
                    <tr>
                        <td colspan="3" class="text-center">暂无数据</td>
                    </tr>
                `;
            }
        }
        
        // 初始加载数据
        loadAnalyticsData();
        
        // 刷新按钮事件
        document.getElementById('refresh-users-btn').addEventListener('click', function() {
            loadAnalyticsData();
        });
        
        document.getElementById('refresh-questions-btn').addEventListener('click', function() {
            loadAnalyticsData();
        });
        
        // 时间周期切换
        document.querySelectorAll('[data-period]').forEach(button => {
            button.addEventListener('click', function() {
                document.querySelectorAll('[data-period]').forEach(btn => {
                    btn.classList.remove('active');
                });
                this.classList.add('active');
                
                // 这里可以根据选择的周期重新加载数据
                // 实际项目中需要添加对应的后端API支持
            });
        });
    });
</script>
{% endblock %}
