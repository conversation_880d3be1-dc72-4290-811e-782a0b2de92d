from flask import Blueprint

# 创建蓝图
auth_bp = Blueprint('auth', __name__)
chat_bp = Blueprint('chat', __name__)
analytics_bp = Blueprint('analytics', __name__)
user_bp = Blueprint('user', __name__)
prompt_bp = Blueprint('prompt', __name__)
system_bp = Blueprint('system', __name__)

# 导入视图
from . import auth, chat, analytics, user, prompt, system

def init_app(app):
    """注册所有蓝图"""
    app.register_blueprint(auth_bp, url_prefix='/auth')
    app.register_blueprint(chat_bp, url_prefix='/chat')
    app.register_blueprint(analytics_bp, url_prefix='/analytics')
    app.register_blueprint(user_bp, url_prefix='/user')
    app.register_blueprint(prompt_bp, url_prefix='/prompt')
    app.register_blueprint(system_bp, url_prefix='/system')
