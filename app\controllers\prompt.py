from flask import render_template, redirect, url_for, flash, request, jsonify
from flask_login import login_required, current_user
from . import prompt_bp
from app.models import db
from app.models.prompt import Prompt, PromptCategory

@prompt_bp.route('/')
@login_required
def index():
    """提示词库页面"""
    # 获取所有分类
    categories = PromptCategory.query.all()
    
    # 获取提示词
    if current_user.is_teacher():
        # 教师可以看到所有提示词
        prompts = Prompt.query.all()
    else:
        # 学生只能看到公开的提示词
        prompts = Prompt.query.filter_by(is_public=True).all()
    
    return render_template('prompts.html', categories=categories, prompts=prompts)

@prompt_bp.route('/management')
@login_required
def management():
    """提示词管理页面"""
    if not current_user.is_teacher():
        flash('您没有权限访问此页面', 'danger')
        return redirect(url_for('main.index'))
    
    # 获取所有分类
    categories = PromptCategory.query.all()
    
    # 获取所有提示词
    prompts = Prompt.query.all()
    
    return render_template('prompt_management.html', categories=categories, prompts=prompts)

@prompt_bp.route('/create', methods=['GET', 'POST'])
@login_required
def create():
    """创建提示词"""
    if request.method == 'POST':
        title = request.form.get('title')
        content = request.form.get('content')
        category_id = request.form.get('category_id')
        tags = request.form.get('tags')
        is_public = request.form.get('is_public') == 'on'
        
        if not title or not content or not category_id:
            flash('请填写所有必填字段', 'danger')
            return redirect(url_for('prompt.create'))
        
        prompt = Prompt(
            title=title,
            content=content,
            category_id=category_id,
            tags=tags,
            is_public=is_public,
            created_by=current_user.id
        )
        
        db.session.add(prompt)
        db.session.commit()
        
        flash('提示词创建成功', 'success')
        return redirect(url_for('prompt.index'))
    
    # GET请求，显示创建表单
    categories = PromptCategory.query.all()
    return render_template('prompt_form.html', categories=categories)

@prompt_bp.route('/<int:prompt_id>')
@login_required
def view(prompt_id):
    """查看提示词详情"""
    prompt = Prompt.query.get_or_404(prompt_id)
    
    # 检查权限
    if not prompt.is_public and not current_user.is_teacher() and prompt.created_by != current_user.id:
        flash('您无权查看此提示词', 'danger')
        return redirect(url_for('prompt.index'))
    
    return render_template('prompt_detail.html', prompt=prompt)

@prompt_bp.route('/api/list')
@login_required
def api_list():
    """API: 获取提示词列表"""
    category_id = request.args.get('category_id', type=int)
    
    query = Prompt.query
    
    # 按分类过滤
    if category_id:
        query = query.filter_by(category_id=category_id)
    
    # 权限过滤
    if not current_user.is_teacher():
        query = query.filter_by(is_public=True)
    
    prompts = query.all()
    
    return jsonify({
        'success': True,
        'prompts': [{
            'id': p.id,
            'title': p.title,
            'content': p.content,
            'category_id': p.category_id,
            'tags': p.tags
        } for p in prompts]
    })

@prompt_bp.route('/api/titles')
@login_required
def api_titles():
    """API: 获取特定分类下的提示词标题"""
    category_id = request.args.get('category_id', type=int)
    
    if not category_id:
        return jsonify({
            'success': False,
            'message': '缺少分类ID参数'
        }), 400
    
    query = Prompt.query.filter_by(category_id=category_id)
    
    # 权限过滤
    if not current_user.is_teacher():
        query = query.filter_by(is_public=True)
    
    prompts = query.all()
    
    return jsonify({
        'success': True,
        'titles': [{
            'id': p.id,
            'title': p.title
        } for p in prompts]
    })

@prompt_bp.route('/categories')
@login_required
def categories():
    """分类管理页面"""
    if not current_user.is_teacher():
        flash('您没有权限访问此页面', 'danger')
        return redirect(url_for('main.index'))
    
    categories = PromptCategory.query.all()
    return render_template('prompt_categories.html', categories=categories)

@prompt_bp.route('/category/create', methods=['POST'])
@login_required
def create_category():
    """创建分类"""
    if not current_user.is_teacher():
        return jsonify({'success': False, 'message': '没有权限'}), 403
    
    name = request.form.get('name')
    description = request.form.get('description', '')
    
    if not name:
        return jsonify({'success': False, 'message': '分类名称不能为空'}), 400
    
    # 检查是否已存在同名分类
    if PromptCategory.query.filter_by(name=name).first():
        return jsonify({'success': False, 'message': '分类名称已存在'}), 400
    
    category = PromptCategory(name=name, description=description)
    db.session.add(category)
    db.session.commit()
    
    return jsonify({
        'success': True,
        'category': {
            'id': category.id,
            'name': category.name,
            'description': category.description
        }
    })

@prompt_bp.route('/category/<int:category_id>/edit', methods=['POST'])
@login_required
def edit_category(category_id):
    """编辑分类"""
    if not current_user.is_teacher():
        return jsonify({'success': False, 'message': '没有权限'}), 403
    
    category = PromptCategory.query.get_or_404(category_id)
    name = request.form.get('name')
    description = request.form.get('description', '')
    
    if not name:
        return jsonify({'success': False, 'message': '分类名称不能为空'}), 400
    
    # 检查是否已存在同名分类（排除自身）
    existing = PromptCategory.query.filter_by(name=name).first()
    if existing and existing.id != category_id:
        return jsonify({'success': False, 'message': '分类名称已存在'}), 400
    
    category.name = name
    category.description = description
    db.session.commit()
    
    return jsonify({
        'success': True,
        'category': {
            'id': category.id,
            'name': category.name,
            'description': category.description
        }
    })

@prompt_bp.route('/category/<int:category_id>/delete', methods=['POST'])
@login_required
def delete_category(category_id):
    """删除分类"""
    if not current_user.is_teacher():
        return jsonify({'success': False, 'message': '没有权限'}), 403
    
    category = PromptCategory.query.get_or_404(category_id)
    
    # 检查分类下是否有提示词
    if Prompt.query.filter_by(category_id=category_id).first():
        return jsonify({'success': False, 'message': '该分类下存在提示词，无法删除'}), 400
    
    db.session.delete(category)
    db.session.commit()
    
    return jsonify({'success': True})

@prompt_bp.route('/<int:prompt_id>/edit', methods=['GET', 'POST'])
@login_required
def edit(prompt_id):
    """编辑提示词"""
    prompt = Prompt.query.get_or_404(prompt_id)
    
    # 检查权限
    if not current_user.is_teacher() and prompt.created_by != current_user.id:
        flash('您无权编辑此提示词', 'danger')
        return redirect(url_for('prompt.index'))
    
    if request.method == 'POST':
        title = request.form.get('title')
        content = request.form.get('content')
        category_id = request.form.get('category_id')
        tags = request.form.get('tags')
        is_public = request.form.get('is_public') == 'on'
        
        if not title or not content or not category_id:
            flash('请填写所有必填字段', 'danger')
            return redirect(url_for('prompt.edit', prompt_id=prompt_id))
        
        prompt.title = title
        prompt.content = content
        prompt.category_id = category_id
        prompt.tags = tags
        prompt.is_public = is_public
        
        db.session.commit()
        
        flash('提示词更新成功', 'success')
        return redirect(url_for('prompt.index'))
    
    # GET请求，显示编辑表单
    categories = PromptCategory.query.all()
    return render_template('prompt_form.html', prompt=prompt, categories=categories, is_edit=True)

@prompt_bp.route('/<int:prompt_id>/delete', methods=['POST'])
@login_required
def delete(prompt_id):
    """删除提示词"""
    prompt = Prompt.query.get_or_404(prompt_id)
    
    # 检查权限
    if not current_user.is_teacher() and prompt.created_by != current_user.id:
        flash('您无权删除此提示词', 'danger')
        return redirect(url_for('prompt.index'))
    
    db.session.delete(prompt)
    db.session.commit()
    
    flash('提示词已删除', 'success')
    return redirect(url_for('prompt.index'))
