{% extends "base.html" %}

{% block title %}智能对话 - 智慧成本核算教学系统{% endblock %}

{% block content %}
<div class="row">
    <!-- 会话列表侧边栏 -->
    <div class="col-md-3">
        <div class="card mb-3 animate__animated animate__fadeIn">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="fas fa-comments me-2"></i>会话列表</h5>
                <button class="btn btn-sm btn-primary" id="new-session-btn">
                    <i class="fas fa-plus"></i> 新会话
                </button>
            </div>
            <div class="list-group list-group-flush" id="sessions-list">
                {% if sessions %}
                    {% for session in sessions %}
                        <a href="#" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center session-item {% if current_session and current_session.id == session.id %}active{% endif %}" data-session-id="{{ session.id }}">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-comment-dots me-2"></i>
                                <div class="text-truncate">{{ session.title }}</div>
                            </div>
                            <small class="text-muted">{{ session.updated_at.strftime('%m-%d %H:%M') }}</small>
                        </a>
                    {% endfor %}
                {% else %}
                    <div class="list-group-item text-center text-muted p-4">
                        <i class="fas fa-comments fa-2x mb-3"></i>
                        <p>正在准备对话</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
    
    <!-- 聊天主界面 -->
    <div class="col-md-9">
        <div class="card animate__animated animate__fadeIn">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0" id="current-session-title">
                    <i class="fas fa-robot me-2"></i>
                    {% if current_session %}
                        {{ current_session.title }}
                    {% else %}
                        智能对话
                    {% endif %}
                </h5>
                <div class="session-actions">
                    <button class="btn btn-sm btn-outline-danger" id="delete-session-btn">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
            <div class="card-body p-0">
                <div class="chat-container">
                    <div class="chat-messages p-3" id="chat-messages">
                        {% if messages %}
                            {% for message in messages %}
                                <div class="message {% if message.role == 'user' %}message-user{% else %}message-assistant{% endif %} mb-3" data-message-id="{{ message.id }}">
                                    <div class="message-header">
                                        {% if message.role == 'user' %}
                                            <i class="fas fa-user me-2"></i>您
                                        {% else %}
                                            <i class="fas fa-robot me-2"></i>AI助手
                                        {% endif %}
                                    </div>
                                    {% if message.role == 'user' and message.category %}
                                        <div class="message-category">
                                            <span class="badge bg-info"><i class="fas fa-tag me-1"></i>{{ message.category.name }}</span>
                                        </div>
                                    {% endif %}
                                    <div class="message-content">
                                        {{ message.content|safe }}
                                    </div>
                                    <div class="message-time small text-muted mt-1">
                                        <i class="fas fa-clock me-1"></i>{{ message.created_at.strftime('%H:%M') }}
                                        {% if message.role == 'assistant' %}
                                            <div class="feedback-buttons mt-2">
                                                <button class="btn btn-sm btn-outline-success feedback-btn" data-feedback="resolved" data-message-id="{{ message.id }}" {% if message.is_resolved == True %}disabled{% endif %}>
                                                    <i class="fas fa-check-circle"></i> 已解决
                                                </button>
                                                <button class="btn btn-sm btn-outline-danger feedback-btn" data-feedback="unresolved" data-message-id="{{ message.id }}" {% if message.is_resolved == False %}disabled{% endif %}>
                                                    <i class="fas fa-times-circle"></i> 未解决
                                                </button>
                                                <button class="btn btn-sm btn-outline-secondary feedback-comment-btn" data-message-id="{{ message.id }}">
                                                    <i class="fas fa-comment"></i> 评价
                                                </button>
                                            </div>
                                            {% if message.feedback %}
                                                <div class="feedback-comment mt-2">
                                                    <i class="fas fa-quote-left me-1"></i> {{ message.feedback }}
                                                </div>
                                            {% endif %}
                                        {% endif %}
                                    </div>
                                </div>
                            {% endfor %}
                        {% else %}
                            <div class="text-center my-5 py-5">
                                <i class="fas fa-robot display-1 text-primary mb-4"></i>
                                <h4>开始一个新的对话</h4>
                                <p class="text-muted">您可以向智能助手询问有关成本核算的问题</p>
                                <div class="mt-4">
                                    <div class="row justify-content-center">
                                        <div class="col-md-8">
                                            <div class="card mb-3 suggestion-card">
                                                <div class="card-body">
                                                    <h6><i class="fas fa-lightbulb text-warning me-2"></i>示例问题</h6>
                                                    <ul class="suggestion-list">
                                                        <li>什么是成本核算？</li>
                                                        <li>如何计算产品单位成本？</li>
                                                        <li>成本核算的基本方法有哪些？</li>
                                                    </ul>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="chat-input p-3 border-top">
                        <form id="chat-form" class="d-flex flex-column">
                            <input type="hidden" id="session-id" value="{{ current_session.id }}">
                            
                            <div class="mb-2">
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-tag"></i></span>
                                    <select class="form-select" id="category-select" required>
                                        <option value="">选择问题分类（必填）</option>
                                        {% for category in categories %}
                                            <option value="{{ category.id }}" data-type="category">{{ category.name }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                            
                            <div class="mb-2" id="prompt-title-container" style="display: none;">
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-list"></i></span>
                                    <select class="form-select" id="prompt-title-select">
                                        <option value="">选择标题（必填）</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="d-flex">
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-keyboard"></i></span>
                                    <textarea class="form-control" id="message-input" rows="2" placeholder="输入您的问题..."></textarea>
                                    <button type="submit" class="btn btn-primary" id="send-btn">
                                        <i class="fas fa-paper-plane"></i> 发送
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 反馈评价模态框 -->
<div class="modal fade" id="feedback-modal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><i class="fas fa-comment-dots me-2"></i>提交问题反馈</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="feedback-form">
                    <input type="hidden" id="feedback-message-id">
                    <div class="mb-3">
                        <label for="feedback-text" class="form-label">您对回答的评价</label>
                        <textarea class="form-control" id="feedback-text" rows="3" placeholder="请输入您的评价内容..."></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="submit-feedback-btn">提交评价</button>
            </div>
        </div>
    </div>
</div>

<!-- 删除会话确认模态框 -->
<div class="modal fade" id="delete-modal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><i class="fas fa-trash me-2"></i>删除会话</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>确定要删除当前会话吗？此操作不可撤销。</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-danger" id="confirm-delete-btn">删除</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<!-- Marked.js for Markdown rendering -->
<script src="https://cdn.jsdelivr.net/npm/marked@9.1.6/marked.min.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 配置marked.js
        marked.setOptions({
            breaks: true,
            gfm: true,
            sanitize: false,
            smartLists: true,
            smartypants: false
        });

        // 渲染Markdown内容的函数
        function renderMarkdown(content) {
            try {
                return marked.parse(content);
            } catch (error) {
                console.error('Markdown渲染错误:', error);
                return content; // 如果渲染失败，返回原始内容
            }
        }

        // 渲染页面上已有的AI消息
        document.querySelectorAll('.message-assistant .message-content').forEach(function(element) {
            const content = element.textContent || element.innerText;
            element.innerHTML = renderMarkdown(content);
        });
        const chatForm = document.getElementById('chat-form');
        const messageInput = document.getElementById('message-input');
        const chatMessages = document.getElementById('chat-messages');
        const sessionIdInput = document.getElementById('session-id');
        const newSessionBtn = document.getElementById('new-session-btn');
        const deleteSessionBtn = document.getElementById('delete-session-btn');
        const confirmDeleteBtn = document.getElementById('confirm-delete-btn');
        const deleteModal = new bootstrap.Modal(document.getElementById('delete-modal'));
        const categorySelect = document.getElementById('category-select');
        const promptTitleContainer = document.getElementById('prompt-title-container');
        const promptTitleSelect = document.getElementById('prompt-title-select');
        const feedbackModal = new bootstrap.Modal(document.getElementById('feedback-modal'));
        const feedbackForm = document.getElementById('feedback-form');
        const feedbackMessageId = document.getElementById('feedback-message-id');
        const feedbackText = document.getElementById('feedback-text');
        const submitFeedbackBtn = document.getElementById('submit-feedback-btn');
        
        // 滚动到底部
        function scrollToBottom() {
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }
        
        // 初始滚动到底部
        scrollToBottom();
        
        // 分类选择变化事件
        categorySelect.addEventListener('change', function() {
            const categoryId = this.value;
            
            if (!categoryId) {
                promptTitleContainer.style.display = 'none';
                promptTitleSelect.innerHTML = '<option value="">选择标题（必填）</option>';
                return;
            }
            
            // 获取分类下的标题列表
            fetch(`/prompt/api/titles?category_id=${categoryId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.titles && data.titles.length > 0) {
                        // 显示标题选择框
                        promptTitleSelect.innerHTML = '<option value="">选择标题（必填）</option>';
                        
                        // 添加标题选项
                        data.titles.forEach(title => {
                            const option = document.createElement('option');
                            option.value = title.id;
                            option.textContent = title.title;
                            promptTitleSelect.appendChild(option);
                        });
                        
                        promptTitleContainer.style.display = 'block';
                    } else {
                        // 没有标题，隐藏标题选择框
                        promptTitleContainer.style.display = 'none';
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    promptTitleContainer.style.display = 'none';
                });
        });
        
        // 删除会话按钮点击事件
        deleteSessionBtn.addEventListener('click', function() {
            deleteModal.show();
        });
        
        // 确认删除按钮点击事件
        confirmDeleteBtn.addEventListener('click', function() {
            const sessionId = sessionIdInput.value;
            if (!sessionId) return;
            
            // 发送删除请求
            fetch(`/chat/session/${sessionId}/delete`, {
                method: 'POST',
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // 重定向到聊天主页
                    window.location.href = '/chat/';
                } else {
                    alert('删除会话失败: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('删除会话失败，请重试');
            });
            
            // 关闭模态框
            deleteModal.hide();
        });
        
        // 发送消息
        chatForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const message = messageInput.value.trim();
            const sessionId = sessionIdInput.value;
            const categoryId = categorySelect.value;
            
            // 验证必填项
            if (!message || !categoryId) {
                if (!categoryId) {
                    alert('请选择问题分类');
                    categorySelect.focus();
                }
                return;
            }
            
            // 如果有标题选择框且可见，则标题也是必填的
            if (promptTitleContainer.style.display !== 'none' && !promptTitleSelect.value) {
                alert('请选择标题');
                promptTitleSelect.focus();
                return;
            }
            
            // 清空输入框
            messageInput.value = '';
            
            // 获取分类名称
            let categoryName = '';
            if (categoryId) {
                const selectedOption = categorySelect.options[categorySelect.selectedIndex];
                categoryName = selectedOption.text;
            }
            
            // 添加用户消息到界面
            const userMessageHtml = `
                <div class="message message-user mb-3">
                    <div class="message-header">
                        <i class="fas fa-user me-2"></i>您
                    </div>
                    ${categoryId ? `<div class="message-category"><span class="badge bg-info"><i class="fas fa-tag me-1"></i>${categoryName}</span></div>` : ''}
                    <div class="message-content">
                        ${message}
                    </div>
                    <div class="message-time small text-muted mt-1">
                        <i class="fas fa-clock me-1"></i>${new Date().toLocaleTimeString('zh-CN', {hour: '2-digit', minute:'2-digit'})}
                    </div>
                </div>
            `;
            chatMessages.innerHTML += userMessageHtml;
            scrollToBottom();
            
            // 显示加载指示器
            const loadingHtml = `
                <div class="message message-assistant mb-3" id="loading-message">
                    <div class="spinner-border spinner-border-sm text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <span class="ms-2">AI思考中...</span>
                </div>
            `;
            chatMessages.innerHTML += loadingHtml;
            scrollToBottom();
            
            // 发送API请求
            fetch('/chat/message', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    session_id: sessionId,
                    content: message,
                    category_id: categoryId || null,
                    prompt_id: promptTitleContainer.style.display !== 'none' ? promptTitleSelect.value : null
                })
            })
            .then(response => response.json())
            .then(data => {
                // 移除加载指示器
                const loadingMessage = document.getElementById('loading-message');
                if (loadingMessage) {
                    loadingMessage.remove();
                }
                
                if (data.success) {
                    // 渲染AI回复的Markdown内容
                    const renderedContent = renderMarkdown(data.ai_message.content);

                    // 添加AI回复
                    const aiMessageHtml = `
                        <div class="message message-assistant mb-3" data-message-id="${data.ai_message.id}">
                            <div class="message-header">
                                <i class="fas fa-robot me-2"></i>AI助手
                            </div>
                            <div class="message-content">
                                ${renderedContent}
                            </div>
                            <div class="message-time small text-muted mt-1">
                                <i class="fas fa-clock me-1"></i>${new Date(data.ai_message.created_at).toLocaleTimeString('zh-CN', {hour: '2-digit', minute:'2-digit'})}
                                <div class="feedback-buttons mt-2">
                                    <button class="btn btn-sm btn-outline-success feedback-btn" data-feedback="resolved" data-message-id="${data.ai_message.id}">
                                        <i class="fas fa-check-circle"></i> 已解决
                                    </button>
                                    <button class="btn btn-sm btn-outline-danger feedback-btn" data-feedback="unresolved" data-message-id="${data.ai_message.id}">
                                        <i class="fas fa-times-circle"></i> 未解决
                                    </button>
                                    <button class="btn btn-sm btn-outline-secondary feedback-comment-btn" data-message-id="${data.ai_message.id}">
                                        <i class="fas fa-comment"></i> 评价
                                    </button>
                                </div>
                            </div>
                        </div>
                    `;
                    chatMessages.innerHTML += aiMessageHtml;
                    scrollToBottom();
                    
                    // 重置分类选择
                    categorySelect.value = '';
                    promptTitleContainer.style.display = 'none';
                } else {
                    // 显示错误消息
                    alert('获取AI回复失败: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                // 移除加载指示器
                const loadingMessage = document.getElementById('loading-message');
                if (loadingMessage) {
                    loadingMessage.remove();
                }
                // 显示错误消息
                alert('发送消息失败，请重试');
            });
        });
        
        // 创建新会话
        newSessionBtn.addEventListener('click', function() {
            const title = '新会话 ' + new Date().toLocaleString('zh-CN', {
                month: 'numeric',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
            });
            
            fetch('/chat/session/new', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `title=${encodeURIComponent(title)}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // 重定向到新会话
                    window.location.href = `/chat/session/${data.session.id}`;
                } else {
                    alert('创建会话失败: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('创建会话失败，请重试');
            });
        });
        
        // 切换会话
        const sessionItems = document.querySelectorAll('.session-item');
        sessionItems.forEach(item => {
            item.addEventListener('click', function(e) {
                e.preventDefault();
                const sessionId = this.getAttribute('data-session-id');
                window.location.href = `/chat/session/${sessionId}`;
            });
        });
        
        // 处理反馈按钮点击
        document.addEventListener('click', function(e) {
            if (e.target.closest('.feedback-btn')) {
                const button = e.target.closest('.feedback-btn');
                const messageId = button.getAttribute('data-message-id');
                const feedbackType = button.getAttribute('data-feedback');
                const isResolved = feedbackType === 'resolved';
                
                // 发送反馈
                submitFeedback(messageId, isResolved);
                
                // 更新按钮状态
                const feedbackButtons = document.querySelectorAll(`.feedback-btn[data-message-id="${messageId}"]`);
                feedbackButtons.forEach(btn => {
                    if (btn.getAttribute('data-feedback') === feedbackType) {
                        btn.disabled = true;
                    } else {
                        btn.disabled = false;
                    }
                });
            }
            
            if (e.target.closest('.feedback-comment-btn')) {
                const button = e.target.closest('.feedback-comment-btn');
                const messageId = button.getAttribute('data-message-id');
                
                // 打开评价模态框
                feedbackMessageId.value = messageId;
                feedbackText.value = '';
                feedbackModal.show();
            }
        });
        
        // 提交评价
        submitFeedbackBtn.addEventListener('click', function() {
            const messageId = feedbackMessageId.value;
            const feedback = feedbackText.value.trim();
            
            if (!messageId) return;
            
            // 获取当前的解决状态
            const resolvedBtn = document.querySelector(`.feedback-btn[data-message-id="${messageId}"][data-feedback="resolved"]`);
            const isResolved = resolvedBtn && resolvedBtn.disabled;
            
            // 发送反馈
            submitFeedback(messageId, isResolved, feedback);
            
            // 关闭模态框
            feedbackModal.hide();
            
            // 添加评价到界面
            if (feedback) {
                const messageElement = document.querySelector(`.message[data-message-id="${messageId}"]`);
                if (messageElement) {
                    let feedbackComment = messageElement.querySelector('.feedback-comment');
                    if (feedbackComment) {
                        feedbackComment.innerHTML = `<i class="fas fa-quote-left me-1"></i> ${feedback}`;
                    } else {
                        const messageTime = messageElement.querySelector('.message-time');
                        if (messageTime) {
                            const feedbackCommentHtml = `
                                <div class="feedback-comment mt-2">
                                    <i class="fas fa-quote-left me-1"></i> ${feedback}
                                </div>
                            `;
                            messageTime.insertAdjacentHTML('beforeend', feedbackCommentHtml);
                        }
                    }
                }
            }
        });
        
        // 提交反馈函数
        function submitFeedback(messageId, isResolved, feedback = '') {
            fetch('/chat/feedback', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    message_id: messageId,
                    is_resolved: isResolved,
                    feedback: feedback
                })
            })
            .then(response => response.json())
            .then(data => {
                if (!data.success) {
                    alert('提交反馈失败: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('提交反馈失败，请重试');
            });
        }
    });
</script>
{% endblock %}
