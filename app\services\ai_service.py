import requests
import json
import os
from http import HTTPStatus
from flask import current_app
from dashscope import Application

def get_ai_response(user_message, session_id=None, context=None):
    """获取AI响应
    
    Args:
        user_message: 用户消息内容
        session_id: 会话ID，用于获取上下文
        context: 额外的上下文信息，如提示词内容
        
    Returns:
        str: AI回复内容
    """
    # 获取API配置
    api_type = current_app.config.get('AI_API_TYPE', 'dashscope')  # 默认使用百炼API
    api_key = current_app.config.get('AI_API_KEY')
    app_id = current_app.config.get('AI_APP_ID')
    
    # 如果没有API密钥，返回默认回复
    if not api_key:
        return "系统未配置AI服务，请联系管理员设置API密钥。"
    
    # 构建系统消息
    system_message = """你是一个专业的成本核算助手，可以帮助学生理解成本核算概念、解决计算问题和分析案例。

请使用Markdown格式来组织你的回答，包括：
- 使用**粗体**来强调重要概念和关键词
- 使用*斜体*来标注术语或变量
- 使用数字列表(1. 2. 3.)或项目符号(- )来组织要点
- 使用`代码格式`来标注公式中的变量
- 使用> 引用格式来展示计算步骤
- 适当使用标题(## ###)来分段

请提供准确、清晰的解答，必要时可以使用公式和步骤分解来解释复杂概念。"""
    
    # 如果有上下文，将其添加到系统消息中
    if context:
        system_message += f"\n\n参考以下提示词模板进行回答：\n{context}"
    
    # 根据API类型选择不同的调用方式
    if api_type == 'dashscope':
        # 使用百炼API
        try:
            # 设置环境变量或直接使用API密钥
            os.environ["DASHSCOPE_API_KEY"] = api_key
            
            response = Application.call(
                api_key=api_key,
                app_id=app_id,
                prompt=f"{system_message}\n\n用户问题：{user_message}"
            )
            
            if response.status_code != HTTPStatus.OK:
                current_app.logger.error(f"百炼API错误: request_id={response.request_id}, code={response.status_code}, message={response.message}")
                return f"抱歉，AI服务暂时不可用: {response.message}"
            else:
                return response.output.text
                
        except Exception as e:
            current_app.logger.error(f"百炼API服务错误: {str(e)}")
            return f"抱歉，AI服务暂时不可用: {str(e)}"
    else:
        # 使用原有的API调用方式
        try:
            # 获取API配置
            model = current_app.config.get('AI_MODEL')
            endpoint = current_app.config.get('AI_ENDPOINT')
            
            # 构建请求数据
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {api_key}"
            }
            
            # 构建消息历史
            messages = []
            
            # 添加系统消息
            messages.append({
                "role": "system",
                "content": system_message
            })
            
            # 添加当前用户消息
            messages.append({
                "role": "user",
                "content": user_message
            })
            
            # 构建请求体
            data = {
                "model": model,
                "messages": messages,
                "temperature": 0.7
            }
            
            # 发送请求
            response = requests.post(endpoint, headers=headers, data=json.dumps(data))
            response.raise_for_status()  # 检查响应状态
            
            # 解析响应
            result = response.json()
            ai_message = result['choices'][0]['message']['content']
            
            return ai_message
            
        except Exception as e:
            current_app.logger.error(f"AI服务错误: {str(e)}")
            return f"抱歉，AI服务暂时不可用: {str(e)}"
