{% extends "base.html" %}

{% block title %}用户管理 - 智慧成本核算教学系统{% endblock %}

{% block extra_css %}
<style>
    .user-table {
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        border-radius: 8px;
        overflow: hidden;
    }
    
    .user-table th {
        background-color: #f8f9fa;
    }
    
    .user-actions {
        white-space: nowrap;
    }
    
    .badge-role {
        font-size: 0.8rem;
        padding: 5px 10px;
        margin-right: 5px;
        border-radius: 20px;
    }
    
    .badge-teacher {
        background-color: #28a745;
        color: white;
    }
    
    .badge-student {
        background-color: #007bff;
        color: white;
    }
    
    .header-actions {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="header-actions">
        <h2>用户管理</h2>
        <div>
            <a href="{{ url_for('user.create') }}" class="btn btn-primary">
                <i class="fas fa-user-plus me-2"></i>创建用户
            </a>
        </div>
    </div>
    
    <div class="card">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover user-table">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>用户名</th>
                            <th>姓名</th>
                            <th>邮箱</th>
                            <th>用户类型</th>
                            <th>状态</th>
                            <th>最后登录</th>
                            <th>创建时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for user in users %}
                        <tr>
                            <td>{{ user.id }}</td>
                            <td>{{ user.username }}</td>
                            <td>{{ user.name or '-' }}</td>
                            <td>{{ user.email }}</td>
                            <td>
                                <span class="badge badge-role {% if user.user_type == 'teacher' %}badge-teacher{% elif user.user_type == 'student' %}badge-student{% endif %}">
                                    {% if user.user_type == 'teacher' %}教师{% else %}学生{% endif %}
                                </span>
                            </td>
                            <td>
                                {% if user.is_active %}
                                <span class="badge bg-success">激活</span>
                                {% else %}
                                <span class="badge bg-danger">禁用</span>
                                {% endif %}
                            </td>
                            <td>{{ user.last_login.strftime('%Y-%m-%d %H:%M') if user.last_login else '从未登录' }}</td>
                            <td>{{ user.created_at.strftime('%Y-%m-%d') }}</td>
                            <td class="user-actions">
                                <a href="{{ url_for('user.edit', user_id=user.id) }}" class="btn btn-sm btn-outline-primary">
                                    <i class="fas fa-edit"></i>
                                </a>
                                {% if user.id != current_user.id %}
                                <button type="button" class="btn btn-sm btn-outline-danger" data-userid="{{ user.id }}" data-username="{{ user.username }}" onclick="confirmDelete(this)">
                                    <i class="fas fa-trash"></i>
                                </button>
                                {% endif %}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- 删除确认模态框 -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">确认删除</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>确定要删除用户 <span id="deleteUsername" class="fw-bold"></span> 吗？此操作不可撤销。</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <form id="deleteForm" method="POST">
                    <button type="submit" class="btn btn-danger">删除</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    function confirmDelete(button) {
        const userId = button.dataset.userid;
        const username = button.dataset.username;
        const modal = new bootstrap.Modal(document.getElementById('deleteModal'));
        document.getElementById('deleteUsername').textContent = username;
        document.getElementById('deleteForm').action = '/user/' + userId + '/delete';
        modal.show();
    }
</script>
{% endblock %}
