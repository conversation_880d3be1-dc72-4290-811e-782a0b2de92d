from flask import render_template, request, jsonify, redirect, url_for
from flask_login import login_required, current_user
from . import chat_bp
from app.models import db
from app.models.chat import ChatSession, ChatMessage
from app.models.prompt import PromptCategory, Prompt
from app.services.ai_service import get_ai_response
from datetime import datetime

@chat_bp.route('/feedback', methods=['POST'])
@login_required
def submit_feedback():
    """提交问题解决反馈"""
    message_id = request.json.get('message_id')
    is_resolved = request.json.get('is_resolved')
    feedback = request.json.get('feedback', '')
    
    if message_id is None or is_resolved is None:
        return jsonify({'success': False, 'message': '缺少必要参数'}), 400
    
    message = ChatMessage.query.get_or_404(message_id)
    
    # 确保用户只能为自己的消息提供反馈
    session = ChatSession.query.get(message.session_id)
    if session.user_id != current_user.id:
        return jsonify({'success': False, 'message': '无权提交此反馈'}), 403
    
    # 更新反馈
    message.is_resolved = bool(is_resolved)
    message.feedback = feedback
    db.session.commit()
    
    return jsonify({
        'success': True,
        'message': '反馈已提交'
    })

@chat_bp.route('/')
@login_required
def index():
    """聊天页面"""
    # 获取用户的聊天会话
    sessions = ChatSession.query.filter_by(user_id=current_user.id).order_by(ChatSession.updated_at.desc()).all()
    
    # 默认打开最新的会话，如果没有则创建新会话
    current_session = None
    messages = None
    
    if sessions:
        current_session = sessions[0]
        messages = ChatMessage.query.filter_by(session_id=current_session.id).order_by(ChatMessage.created_at).all()
    else:
        # 自动创建新会话
        title = '新会话 ' + datetime.now().strftime('%m-%d %H:%M')
        current_session = ChatSession(
            title=title,
            user_id=current_user.id
        )
        db.session.add(current_session)
        db.session.commit()
        
        # 刷新会话列表
        sessions = [current_session]
        messages = []
    
    # 获取所有问题分类
    categories = PromptCategory.query.all()
    
    return render_template(
        'chat.html', 
        sessions=sessions, 
        current_session=current_session,
        messages=messages,
        categories=categories
    )

@chat_bp.route('/session/new', methods=['POST'])
@login_required
def new_session():
    """创建新的聊天会话"""
    title = request.form.get('title', '新会话')
    
    session = ChatSession(
        title=title,
        user_id=current_user.id
    )
    
    db.session.add(session)
    db.session.commit()
    
    return jsonify({
        'success': True,
        'session': {
            'id': session.id,
            'title': session.title
        }
    })

@chat_bp.route('/session/<int:session_id>/delete', methods=['POST'])
@login_required
def delete_session(session_id):
    """删除聊天会话"""
    session = ChatSession.query.get_or_404(session_id)
    
    # 确保用户只能删除自己的会话
    if session.user_id != current_user.id:
        return jsonify({'success': False, 'message': '无权删除此会话'}), 403
    
    # 删除会话的所有消息
    ChatMessage.query.filter_by(session_id=session_id).delete()
    
    # 删除会话
    db.session.delete(session)
    db.session.commit()
    
    return jsonify({
        'success': True,
        'message': '会话已删除'
    })

@chat_bp.route('/session/<int:session_id>')
@login_required
def get_session(session_id):
    """获取聊天会话详情"""
    # 获取指定会话
    session = ChatSession.query.get_or_404(session_id)
    
    # 确保用户只能访问自己的会话
    if session.user_id != current_user.id:
        return render_template('error.html', error_code=403, error_message='您无权访问此会话'), 403
    
    # 获取所有会话
    sessions = ChatSession.query.filter_by(user_id=current_user.id).order_by(ChatSession.updated_at.desc()).all()
    
    # 获取当前会话的消息
    messages = ChatMessage.query.filter_by(session_id=session_id).order_by(ChatMessage.created_at).all()
    
    # 获取所有问题分类
    categories = PromptCategory.query.all()
    
    return render_template(
        'chat.html',
        sessions=sessions,
        current_session=session,
        messages=messages,
        categories=categories
    )

@chat_bp.route('/message', methods=['POST'])
@login_required
def send_message():
    """发送消息并获取AI回复"""
    session_id = request.json.get('session_id')
    content = request.json.get('content')
    category_id = request.json.get('category_id')
    prompt_id = request.json.get('prompt_id')  # 新增：标题ID
    
    if not session_id or not content or not category_id:
        return jsonify({'success': False, 'message': '缺少必要参数'}), 400
    
    session = ChatSession.query.get_or_404(session_id)
    
    # 确保用户只能访问自己的会话
    if session.user_id != current_user.id:
        return jsonify({'success': False, 'message': '无权访问此会话'}), 403
    
    # 如果提供了提示词ID，获取提示词内容
    prompt_content = None
    if prompt_id:
        prompt = Prompt.query.get(prompt_id)
        if prompt:
            prompt_content = prompt.content
    
    # 保存用户消息
    user_message = ChatMessage(
        session_id=session_id,
        role='user',
        content=content,
        category_id=category_id
    )
    db.session.add(user_message)
    db.session.commit()
    
    # 获取AI回复
    # 如果有提示词内容，将其作为上下文传递给AI服务
    if prompt_content:
        ai_response = get_ai_response(content, session_id, context=prompt_content)
    else:
        ai_response = get_ai_response(content, session_id)
    
    # 保存AI回复
    ai_message = ChatMessage(
        session_id=session_id,
        role='assistant',
        content=ai_response
    )
    db.session.add(ai_message)
    
    # 更新会话时间
    session.updated_at = datetime.utcnow()
    db.session.commit()
    
    # 获取分类名称
    category_name = None
    if category_id:
        category = PromptCategory.query.get(category_id)
        if category:
            category_name = category.name
    
    return jsonify({
        'success': True,
        'user_message': {
            'id': user_message.id,
            'content': user_message.content,
            'category_id': user_message.category_id,
            'category_name': category_name,
            'created_at': user_message.created_at.isoformat()
        },
        'ai_message': {
            'id': ai_message.id,
            'content': ai_message.content,
            'created_at': ai_message.created_at.isoformat()
        }
    })

@chat_bp.route('/create', methods=['GET', 'POST'])
@login_required
def create():
    """创建新会话并使用提示词"""
    prompt_id = request.args.get('prompt_id')
    
    # 创建新会话
    title = '新会话 ' + datetime.now().strftime('%m-%d %H:%M')
    session = ChatSession(
        title=title,
        user_id=current_user.id
    )
    
    db.session.add(session)
    db.session.commit()
    
    # 如果有提示词，可以在这里处理
    if prompt_id:
        prompt = Prompt.query.get(prompt_id)
        if prompt:
            # 创建系统消息
            system_message = ChatMessage(
                session_id=session.id,
                role='system',
                content=f"使用提示词模板: {prompt.title}",
                category_id=prompt.category_id
            )
            db.session.add(system_message)
            
            # 创建用户消息
            user_message = ChatMessage(
                session_id=session.id,
                role='user',
                content=prompt.content,
                category_id=prompt.category_id
            )
            db.session.add(user_message)
            db.session.commit()
            
            # 获取AI回复
            ai_response = get_ai_response(prompt.content, session.id)
            
            # 保存AI回复
            ai_message = ChatMessage(
                session_id=session.id,
                role='assistant',
                content=ai_response
            )
            db.session.add(ai_message)
            db.session.commit()
    
    # 重定向到新会话
    return redirect(url_for('chat.get_session', session_id=session.id))
