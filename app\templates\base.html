<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}智慧成本核算教学系统{% endblock %}</title>
    <!-- Bootstrap 5.3 -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome 图标 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <!-- Animate.css 动画库 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">
    <!-- 自定义样式 -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/main.css') }}">
    {% block styles %}{% endblock %}
</head>
<body class="bg-light">
    <!-- 顶部导航栏 -->
    <header class="navbar navbar-expand-md navbar-dark sticky-top bg-primary shadow-sm">
        <div class="container-fluid">
            <a class="navbar-brand fw-bold animate__animated animate__fadeIn" href="{{ url_for('index') }}">
                <i class="fas fa-graduation-cap me-2"></i>智慧成本核算教学系统
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#sidebarMenu">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            {% if current_user.is_authenticated %}
            <div class="ms-auto d-flex align-items-center">
                <div class="dropdown">
                    <a href="#" class="d-flex align-items-center text-white text-decoration-none dropdown-toggle" id="userDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                        <span class="me-2">{{ current_user.name or current_user.username }}</span>
                        <div class="avatar-circle bg-white text-primary">
                            <span>{{ (current_user.name or current_user.username)[0] | upper }}</span>
                        </div>
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end shadow-sm border-0 mt-2" aria-labelledby="userDropdown">
                        <li><a class="dropdown-item" href="{{ url_for('user.profile') }}"><i class="fas fa-user-circle me-2"></i>个人资料</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="{{ url_for('auth.logout') }}"><i class="fas fa-sign-out-alt me-2"></i>退出登录</a></li>
                    </ul>
                </div>
            </div>
            {% endif %}
        </div>
    </header>

    <div class="container-fluid">
        <div class="row">
            {% if current_user.is_authenticated %}
            <!-- 左侧菜单栏 -->
            <nav id="sidebarMenu" class="col-md-3 col-lg-2 d-md-block bg-white sidebar collapse shadow-sm">
                <div class="position-sticky pt-3">
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link {% if request.endpoint == 'chat.index' %}active{% endif %}" href="{{ url_for('chat.index') }}">
                                <i class="fas fa-comments me-2"></i> 智能对话
                            </a>
                        </li>
                        {% if current_user.is_teacher() %}
                        <li class="nav-item">
                            <a class="nav-link {% if request.endpoint == 'prompt.index' %}active{% endif %}" href="{{ url_for('prompt.index') }}">
                                <i class="fas fa-lightbulb me-2"></i> 提示词库
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if request.endpoint == 'analytics.index' %}active{% endif %}" href="{{ url_for('analytics.index') }}">
                                <i class="fas fa-chart-line me-2"></i> 数据分析
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if request.endpoint == 'user.management' %}active{% endif %}" href="{{ url_for('user.management') }}">
                                <i class="fas fa-users me-2"></i> 用户管理
                            </a>
                        </li>
                        {% endif %}
                    </ul>
                    
                    <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-muted">
                        <span>系统信息</span>
                    </h6>
                    <ul class="nav flex-column mb-2">
                        <li class="nav-item">
                            <a class="nav-link {% if request.endpoint == 'system.about' %}active{% endif %}" href="{{ url_for('system.about') }}">
                                <i class="fas fa-info-circle me-2"></i> 关于系统
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if request.endpoint == 'system.help' %}active{% endif %}" href="{{ url_for('system.help') }}">
                                <i class="fas fa-question-circle me-2"></i> 使用帮助
                            </a>
                        </li>
                        {% if current_user.is_admin() %}
                        <li class="nav-item">
                            <a class="nav-link {% if request.endpoint == 'system.settings' %}active{% endif %}" href="{{ url_for('system.settings') }}">
                                <i class="fas fa-cogs me-2"></i> 系统设置
                            </a>
                        </li>
                        {% endif %}
                    </ul>
                </div>
            </nav>
            {% endif %}

            <!-- 主内容区域 -->
            <main class="{% if current_user.is_authenticated %}col-md-9 ms-sm-auto col-lg-10 px-md-4{% else %}col-12{% endif %}">
                <div class="pt-3 pb-2 mb-3">
                    {% with messages = get_flashed_messages(with_categories=true) %}
                        {% if messages %}
                            {% for category, message in messages %}
                                <div class="alert alert-{{ category }} alert-dismissible fade show animate__animated animate__fadeIn shadow-sm">
                                    {{ message }}
                                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                                </div>
                            {% endfor %}
                        {% endif %}
                    {% endwith %}

                    {% block content %}{% endblock %}
                </div>
            </main>
        </div>
    </div>

    <!-- 页脚 -->
    <footer class="footer mt-auto py-3 bg-white border-top">
        <div class="container text-center">
            <span class="text-muted">© 2025 智慧成本核算教学系统 | 版权所有</span>
        </div>
    </footer>

    <!-- JavaScript 库 -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/jquery@3.7.1/dist/jquery.min.js"></script>
    <!-- ECharts 图表库 -->
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <!-- ApexCharts 图表库 -->
    <script src="https://cdn.jsdelivr.net/npm/apexcharts@3.41.1/dist/apexcharts.min.js"></script>
    <!-- 自定义脚本 -->
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>
    {% block scripts %}{% endblock %}
</body>
</html>
