from flask_wtf import FlaskForm
from wtforms import <PERSON>Field, PasswordField, BooleanField, SubmitField, SelectMultipleField
from wtforms.validators import DataRequired, Length, Email, EqualTo, ValidationError

from app.models.user import User

def login_form():
    """创建登录表单"""
    class LoginForm(FlaskForm):
        username = StringField('用户名', validators=[DataRequired(), Length(1, 64)])
        password = PasswordField('密码', validators=[DataRequired()])
        remember_me = BooleanField('记住我')
        submit = SubmitField('登录')
    
    return LoginForm()

def create_user_form():
    """创建用户表单"""
    class CreateUserForm(FlaskForm):
        username = StringField('用户名', validators=[DataRequired(), Length(1, 64)])
        email = StringField('邮箱', validators=[DataRequired(), Email()])
        name = StringField('姓名', validators=[Length(0, 64)])
        password = PasswordField('密码', validators=[
            DataRequired(),
            Length(min=6, message='密码长度至少为6个字符')
        ])
        confirm_password = PasswordField('确认密码', validators=[
            DataRequired(),
            EqualTo('password', message='两次输入的密码不一致')
        ])
        is_active = BooleanField('激活账户', default=True)
        roles = SelectMultipleField('角色', coerce=int)
        submit = SubmitField('创建')
        
        def validate_username(self, field):
            if User.query.filter_by(username=field.data).first():
                raise ValidationError('用户名已被使用')
                
        def validate_email(self, field):
            if User.query.filter_by(email=field.data).first():
                raise ValidationError('邮箱已被使用')
    
    return CreateUserForm()

def edit_user_form(obj=None):
    """编辑用户表单"""
    class EditUserForm(FlaskForm):
        username = StringField('用户名', validators=[DataRequired(), Length(1, 64)])
        email = StringField('邮箱', validators=[DataRequired(), Email()])
        name = StringField('姓名', validators=[Length(0, 64)])
        password = PasswordField('密码 (留空则不修改)', validators=[
            Length(min=6, message='密码长度至少为6个字符'),
            EqualTo('confirm_password', message='两次输入的密码不一致')
        ])
        confirm_password = PasswordField('确认密码')
        is_active = BooleanField('激活账户')
        roles = SelectMultipleField('角色', coerce=int)
        submit = SubmitField('保存')
        
        def validate_username(self, field):
            if obj and obj.username != field.data and \
                    User.query.filter_by(username=field.data).first():
                raise ValidationError('用户名已被使用')
                
        def validate_email(self, field):
            if obj and obj.email != field.data and \
                    User.query.filter_by(email=field.data).first():
                raise ValidationError('邮箱已被使用')
    
    return EditUserForm(obj=obj)

def require_teacher(func):
    """教师权限装饰器"""
    from functools import wraps
    from flask import abort
    from flask_login import current_user
    
    @wraps(func)
    def decorated_function(*args, **kwargs):
        if not current_user.is_teacher():
            abort(403)
        return func(*args, **kwargs)
    return decorated_function
