{% extends "base.html" %}

{% block title %}关于系统 - 智慧成本核算教学系统{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="row">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">关于系统</h4>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4 mb-4 mb-md-0 text-center">
                            <div class="p-4">
                                <img src="{{ url_for('static', filename='images/placeholder.jpg') }}" alt="系统标志" class="img-fluid rounded-circle mb-3" style="max-width: 200px;">
                                <h3 class="mb-3">智慧成本核算教学系统</h3>
                                <p class="text-muted">版本 1.0.0</p>
                            </div>
                        </div>
                        <div class="col-md-8">
                            <h4>系统简介</h4>
                            <p class="lead">智慧成本核算教学系统是一款基于人工智能技术的教学辅助平台，旨在帮助学生更好地理解和掌握成本核算相关知识。</p>
                            
                            <hr class="my-4">
                            
                            <h5>核心功能</h5>
                            <ul class="list-unstyled">
                                <li class="mb-2">
                                    <i class="fas fa-comments text-primary me-2"></i>
                                    <strong>智能对话</strong>：基于大型语言模型的智能问答系统，解答成本核算相关问题
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-lightbulb text-warning me-2"></i>
                                    <strong>提示词库</strong>：精选的专业提示词，帮助学生更好地与AI助手交流
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-chart-line text-success me-2"></i>
                                    <strong>数据分析</strong>：对学生学习行为进行分析，为教师提供教学决策参考
                                </li>
                            </ul>
                            
                            <hr class="my-4">
                            
                            <h5>技术架构</h5>
                            <p>本系统基于Python Flask框架开发，采用前后端分离架构，后端API与主流大型语言模型集成，为用户提供智能化的教学服务。</p>
                            
                            <hr class="my-4">
                            
                            <h5>联系我们</h5>
                            <p>如有任何问题或建议，请联系系统管理员：<a href="mailto:<EMAIL>"><EMAIL></a></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 