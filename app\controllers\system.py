from flask import render_template, request, redirect, url_for, flash, current_app
from flask_login import login_required, current_user
from . import system_bp
import os
import json
from pathlib import Path

@system_bp.route('/about')
@login_required
def about():
    """关于系统页面"""
    return render_template('about.html')

@system_bp.route('/help')
@login_required
def help():
    """使用帮助页面"""
    return render_template('help.html')

@system_bp.route('/settings', methods=['GET', 'POST'])
@login_required
def settings():
    """系统设置页面"""
    # 检查是否为管理员
    if not current_user.is_admin():
        flash('您没有权限访问此页面', 'danger')
        return redirect(url_for('main.index'))
    
    # 获取当前配置
    config = {
        'AI_API_TYPE': current_app.config.get('AI_API_TYPE', 'dashscope'),
        'AI_API_KEY': current_app.config.get('AI_API_KEY', ''),
        'AI_APP_ID': current_app.config.get('AI_APP_ID', ''),
        'AI_MODEL': current_app.config.get('AI_MODEL', 'gpt-3.5-turbo'),
        'AI_ENDPOINT': current_app.config.get('AI_ENDPOINT', 'https://api.openai.com/v1/chat/completions')
    }
    
    if request.method == 'POST':
        # 更新配置
        config['AI_API_TYPE'] = request.form.get('ai_api_type', 'dashscope')
        config['AI_API_KEY'] = request.form.get('ai_api_key', '')
        config['AI_APP_ID'] = request.form.get('ai_app_id', '')
        config['AI_MODEL'] = request.form.get('ai_model', 'gpt-3.5-turbo')
        config['AI_ENDPOINT'] = request.form.get('ai_endpoint', 'https://api.openai.com/v1/chat/completions')
        
        # 保存到.env文件
        env_path = Path('.env')
        env_content = ""
        
        # 如果.env文件存在，读取现有内容
        if env_path.exists():
            with open(env_path, 'r', encoding='utf-8') as f:
                env_content = f.read()
        
        # 更新或添加配置项
        env_lines = env_content.split('\n')
        updated_keys = set()
        
        for i, line in enumerate(env_lines):
            if '=' in line:
                key = line.split('=')[0].strip()
                if key in config:
                    env_lines[i] = f"{key}={config[key]}"
                    updated_keys.add(key)
        
        # 添加未更新的配置项
        for key, value in config.items():
            if key not in updated_keys:
                env_lines.append(f"{key}={value}")
        
        # 写入.env文件
        with open(env_path, 'w', encoding='utf-8') as f:
            f.write('\n'.join(env_lines))
        
        # 更新当前应用配置
        for key, value in config.items():
            current_app.config[key] = value
            os.environ[key] = value
        
        flash('系统设置已更新', 'success')
        return redirect(url_for('system.settings'))
    
    return render_template('system_settings.html', config=config) 