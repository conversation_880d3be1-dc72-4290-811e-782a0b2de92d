from datetime import datetime
from . import db
from app.models.prompt import PromptCategory

class ChatSession(db.Model):
    """聊天会话模型"""
    __tablename__ = 'chat_sessions'
    
    id = db.Column(db.Integer, primary_key=True)
    title = db.Column(db.String(128))
    user_id = db.Column(db.Inte<PERSON>, db.ForeignKey('users.id'))
    prompt_id = db.Column(db.Integer, db.ForeignKey('prompts.id'), nullable=True)  # 关联的提示词ID
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关系
    messages = db.relationship('ChatMessage', backref='session', lazy='dynamic')
    prompt = db.relationship('Prompt', backref='sessions', lazy=True)
    
    def __repr__(self):
        return f'<ChatSession {self.id}>'

class ChatMessage(db.Model):
    """聊天消息模型"""
    __tablename__ = 'chat_messages'
    
    id = db.Column(db.Integer, primary_key=True)
    session_id = db.Column(db.Integer, db.ForeignKey('chat_sessions.id'))
    role = db.Column(db.String(20))  # 'user' 或 'assistant'
    content = db.Column(db.Text)
    category_id = db.Column(db.Integer, db.ForeignKey('prompt_categories.id'), nullable=True)  # 问题分类
    is_resolved = db.Column(db.Boolean, nullable=True)  # 问题是否已解决
    feedback = db.Column(db.Text, nullable=True)  # 用户反馈
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # 关系
    category = db.relationship('PromptCategory', backref='messages', lazy=True)
    
    def __repr__(self):
        return f'<ChatMessage {self.id}>'
