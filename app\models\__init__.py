from flask_sqlalchemy import SQLAlchemy
from flask_login import LoginManager

# 初始化数据库
db = SQLAlchemy()

# 初始化登录管理器
login_manager = LoginManager()
login_manager.login_view = 'auth.login'
login_manager.login_message = '请先登录以访问此页面'
login_manager.login_message_category = 'info'

def init_app(app):
    """初始化应用"""
    db.init_app(app)
    login_manager.init_app(app)
    
    # 导入模型以确保它们在创建表之前被加载
    from .user import User, Role, UserRole
    from .chat import ChatSession, ChatMessage
    from .prompt import Prompt, PromptCategory
    
    # 创建数据库表
    with app.app_context():
        db.create_all()
