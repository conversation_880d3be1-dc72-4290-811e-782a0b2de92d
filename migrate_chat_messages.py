import sqlite3
import os
from pathlib import Path

def migrate_database():
    """更新数据库结构，添加缺少的列"""
    # 获取数据库文件路径
    db_path = Path('instance/jiaoshi.db')
    
    if not db_path.exists():
        print(f"数据库文件不存在: {db_path}")
        return False
    
    try:
        # 连接数据库
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查 chat_messages 表中是否存在需要的列
        cursor.execute("PRAGMA table_info(chat_messages)")
        columns = [column[1] for column in cursor.fetchall()]
        print(f"当前表中的列: {columns}")
        
        # 如果不存在 category_id 列，则添加
        if 'category_id' not in columns:
            print("添加 category_id 列到 chat_messages 表...")
            cursor.execute("ALTER TABLE chat_messages ADD COLUMN category_id INTEGER REFERENCES prompt_categories(id)")
            conn.commit()
            print("category_id 列添加成功！")
        else:
            print("category_id 列已存在，无需添加")
        
        # 如果不存在 is_resolved 列，则添加
        if 'is_resolved' not in columns:
            print("添加 is_resolved 列到 chat_messages 表...")
            cursor.execute("ALTER TABLE chat_messages ADD COLUMN is_resolved BOOLEAN")
            conn.commit()
            print("is_resolved 列添加成功！")
        else:
            print("is_resolved 列已存在，无需添加")
        
        # 如果不存在 feedback 列，则添加
        if 'feedback' not in columns:
            print("添加 feedback 列到 chat_messages 表...")
            cursor.execute("ALTER TABLE chat_messages ADD COLUMN feedback TEXT")
            conn.commit()
            print("feedback 列添加成功！")
        else:
            print("feedback 列已存在，无需添加")
        
        print("数据库迁移完成！")
        conn.close()
        return True
        
    except Exception as e:
        print(f"数据库迁移失败: {e}")
        return False

if __name__ == "__main__":
    migrate_database() 